<div class="settings-container">
  <h2 class="settings-page-title">Settings</h2>

  <div *ngIf="isLoading" class="loading-container">
    <mat-progress-spinner mode="indeterminate" diameter="50"></mat-progress-spinner>
    <p>Loading settings...</p>
  </div>

  <div *ngIf="!isLoading && profile" class="settings-content content-fade-in"> <!-- Apply fade-in here -->
    <mat-card class="settings-section profile-info-card">
      <mat-card-header>
        <mat-card-title>Profile</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p class="mat-body-1"><strong>Email:</strong> {{ userDisplayEmail }}</p>
        <p class="mat-body-1"><strong>Status:</strong> {{ profile.subscription_status | titlecase }} User</p>
        <p *ngIf="profile.is_admin" class="mat-body-1"><strong>Admin:</strong> Yes</p>
      </mat-card-content>
    </mat-card>

    <mat-expansion-panel class="settings-section contact-form-panel">
      <mat-expansion-panel-header>
        <mat-panel-title>
          Contact / Feedback
        </mat-panel-title>
        <mat-panel-description>
          Send feedback or contact administrators
        </mat-panel-description>
      </mat-expansion-panel-header>
      <p class="mat-caption section-description">Use this form to send feedback or contact the administrators.</p>
      <form [formGroup]="contactForm" (ngSubmit)="onSubmitContactForm()" class="contact-form-material">
        <mat-form-field appearance="outline" class="full-width-field">
          <mat-label>Subject</mat-label>
          <input matInput formControlName="subject" placeholder="e.g., Feature Request">
          <mat-error *ngIf="subject?.invalid && (subject?.dirty || subject?.touched) && subject?.errors?.['required']">
            Subject is required.
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width-field">
          <mat-label>Message</mat-label>
          <textarea matInput formControlName="message" rows="4" placeholder="Your message here..."></textarea>
          <mat-error *ngIf="message?.invalid && (message?.dirty || message?.touched) && message?.errors?.['required']">
            Message is required.
          </mat-error>
        </mat-form-field>

        <div *ngIf="contactErrorMessage && !contactSuccessMessage" class="server-message error-message">
          <mat-icon>error_outline</mat-icon> <span>{{ contactErrorMessage }}</span>
        </div>
        <div *ngIf="contactSuccessMessage" class="server-message success-message">
           <mat-icon>check_circle_outline</mat-icon> <span>{{ contactSuccessMessage }}</span>
        </div>

        <div class="form-actions">
          <button mat-raised-button color="primary" type="submit" [disabled]="contactForm.invalid || isContactSubmitting">
            <mat-icon *ngIf="!isContactSubmitting">send</mat-icon>
            <mat-progress-spinner *ngIf="isContactSubmitting" mode="indeterminate" diameter="20" class="button-spinner"></mat-progress-spinner>
            {{ isContactSubmitting ? 'Sending...' : 'Send Message' }}
          </button>
        </div>
      </form>
    </mat-expansion-panel>

    <mat-card class="settings-section legal-links-card">
      <mat-card-header>
        <mat-card-title>Legal</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p><a routerLink="/privacy-policy" class="app-link">Privacy Policy</a></p> <!-- Assuming you might have routes for these -->
        <p><a routerLink="/terms-of-service" class="app-link">Terms of Service</a></p>
      </mat-card-content>
    </mat-card>

    <mat-card class="settings-section admin-access-card" *ngIf="profile.is_admin">
      <mat-card-header>
        <mat-card-title>Admin</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <button mat-raised-button color="accent" (click)="navigateToAdmin()" class="full-width-button">
          <mat-icon>admin_panel_settings</mat-icon> Access Admin Panel
        </button>
      </mat-card-content>
    </mat-card>

    <mat-card class="settings-section logout-card">
      <mat-card-content>
        <button mat-stroked-button color="warn" (click)="handleLogout()" [disabled]="isLoading" class="full-width-button">
          <mat-icon *ngIf="!isLoading">exit_to_app</mat-icon>
          <mat-progress-spinner *ngIf="isLoading" mode="indeterminate" diameter="20" class="button-spinner"></mat-progress-spinner>
          {{ isLoading ? 'Logging out...' : 'Logout' }}
        </button>
      </mat-card-content>
    </mat-card>
  </div>

  <div *ngIf="!isLoading && !profile && !errorMessage" class="no-profile-message">
    <mat-icon>warning_amber</mat-icon>
    <p>Could not load profile information.</p>
  </div>
  <div *ngIf="!isLoading && errorMessage && !profile" class="server-message error-message general-error-message">
     <mat-icon>error_outline</mat-icon> <span>Error loading settings: {{ errorMessage }}</span>
  </div>
</div>