<!-- Authenticated Layout -->
<ng-container *ngIf="isAuthenticated">
  <mat-sidenav-container class="app-sidenav-container">
    <!-- Sidenav (Sidebar) - Dynamic mode and opened state based on screen size -->
    <mat-sidenav #sidenav [mode]="sidenavMode" [opened]="sidenavOpened" class="app-sidenav">
      <app-sidebar></app-sidebar>
    </mat-sidenav>

    <!-- Main Content -->
    <mat-sidenav-content class="app-sidenav-content">
      <!-- Toolbar -->
      <mat-toolbar color="primary" class="app-toolbar">
        <button mat-icon-button (click)="sidenav.toggle()" class="sidenav-toggle-button">
          <mat-icon>menu</mat-icon>
        </button>
        <span>{{ title }}</span>
        <!-- Search bar can be integrated here or remain below -->
      </mat-toolbar>

      <!-- Search bar - placed directly below the toolbar -->
      <app-search-bar class="app-search-bar-sticky"></app-search-bar>

      <!-- Router Outlet for page content -->
      <div class="main-content-area">
        <router-outlet></router-outlet>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>

  <!-- Tab bar for mobile - remains outside sidenav container, shown via CSS media queries -->
  <app-tab-bar class="mobile-tab-bar"></app-tab-bar>
</ng-container>

<!-- Unauthenticated Layout - Just the router outlet for auth pages -->
<ng-container *ngIf="!isAuthenticated">
  <div class="auth-container">
    <router-outlet></router-outlet>
  </div>
</ng-container>