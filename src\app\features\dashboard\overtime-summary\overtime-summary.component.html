<mat-card class="overtime-summary-card">
  <mat-card-content>
    <div class="summary-header">
      <h3 class="summary-title">
        Overtime Summary
        <mat-icon
          class="summary-title-tooltip"
          matTooltip="An overtime period is defined by administrators and may span multiple days or weeks"
          aria-label="Overtime period definition tooltip">info_outline</mat-icon>
      </h3>
      <div class="summary-nav">
        <button mat-icon-button color="primary" (click)="navigateToPreviousPeriod()" [disabled]="!canNavigateToPrevious || isLoading" aria-label="Previous period">
          <mat-icon>chevron_left</mat-icon>
        </button>
        <span class="summary-nav-label">OT Period</span>
        <button mat-icon-button color="primary" (click)="navigateToNextPeriod()" [disabled]="!canNavigateToNext || isLoading" aria-label="Next period">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
    </div>
    <p *ngIf="!currentOtPeriod && !isLoading && !errorMessage" class="no-data-message">
      No OT periods available.
    </p>

    <div *ngIf="currentOtPeriod" class="period-indicator">
      <span class="period-indicator-text">
        {{ formatDateRange(currentOtPeriod.start_date, currentOtPeriod.end_date) }}
      </span>
    </div>

    <div *ngIf="isLoading" class="summary-loader">
      <mat-progress-spinner mode="indeterminate" diameter="30"></mat-progress-spinner>
      <span>Loading overtime data...</span>
    </div>

    <div *ngIf="errorMessage" class="summary-alert error-alert">
      <mat-icon>error_outline</mat-icon>
      <span>{{ errorMessage }}</span>
    </div>

    <div *ngIf="!isLoading && !errorMessage && currentOtPeriod" class="stats-container">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-label">
            Total OT Hours
            <mat-icon
              class="stat-tooltip"
              matTooltip="Total overtime hours logged during this OT period (both voluntary and mandatory)"
              aria-label="Total OT hours tooltip">info_outline</mat-icon>
          </div>
          <div class="stat-value">{{ otHours }} hrs</div>
        </div>

        <div class="stat-item">
          <div class="stat-label">
            Mandatory Holds
            <mat-icon
              class="stat-tooltip"
              matTooltip="Number of mandatory overtime shifts that you've marked to count towards your monthly holdover total"
              aria-label="Mandatory holds tooltip">info_outline</mat-icon>
          </div>
          <div class="stat-value">{{ mandatoryHoldCount }}</div>
        </div>

        <div class="stat-item">
          <div class="stat-label">
            Voluntary OT
            <mat-icon
              class="stat-tooltip"
              matTooltip="Number of voluntary overtime shifts logged during this OT period"
              aria-label="Voluntary OT tooltip">info_outline</mat-icon>
          </div>
          <div class="stat-value">{{ voluntaryHoldCount }}</div>
        </div>
      </div>
    </div>

    <div *ngIf="!isLoading && !errorMessage && !currentOtPeriod" class="no-data-message">
      <p>No overtime periods have been defined by an administrator.</p>
    </div>
  </mat-card-content>
</mat-card>
