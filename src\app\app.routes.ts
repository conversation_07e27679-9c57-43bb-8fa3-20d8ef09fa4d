// src/app/app.routes.ts
import { Routes } from '@angular/router';
import { authGuard } from './core/guards/auth.guard';
import { adminGuard } from './core/guards/admin.guard';
import { MainLayoutComponent } from './layout/main-layout/main-layout.component';

// Import feature components
import { DashboardComponent } from './features/dashboard/dashboard.component';
import { CalendarComponent } from './features/calendar/calendar.component';
import { SettingsComponent } from './features/settings/settings.component';
import { AdminComponent } from './features/admin/admin.component';


export const routes: Routes = [
  // Authentication routes (public) - using lazy loading with standalone components
  {
    path: 'auth',
    children: [
      {
        path: 'login',
        loadComponent: () => import('./auth/login/login.component').then(c => c.LoginComponent)
      },
      {
        path: 'signup',
        loadComponent: () => import('./auth/signup/signup.component').then(c => c.SignupComponent)
      },
      {
        path: 'verification-success',
        loadComponent: () => import('./auth/verification-success/verification-success.component').then(c => c.VerificationSuccessComponent)
      },
      { path: '', redirectTo: 'login', pathMatch: 'full' }
    ]
  },

  // Main application routes (protected by authGuard)
  {
    path: '', // Use base path for the main layout
    component: MainLayoutComponent,
    canActivate: [authGuard], // Apply the guard here
    children: [
      { path: 'dashboard', component: DashboardComponent },
      { path: 'calendar', component: CalendarComponent },
      { path: 'settings', component: SettingsComponent },
      { path: 'admin', component: AdminComponent, canActivate: [adminGuard] },
      // Default route within the authenticated layout redirects to dashboard
      { path: '', redirectTo: 'dashboard', pathMatch: 'full' }
    ]
  },

  // Optional: Redirect any unknown paths for logged-in users to dashboard
  // This might conflict if you want a specific 404 page later
  // { path: '**', redirectTo: '/dashboard', canActivate: [authGuard] }

  // Fallback for unauthenticated / non-matching routes could also go here if needed
   { path: '**', redirectTo: '/auth/login' } // Or a dedicated 404 page

];