// src/app/shared/directives/feature-flag.directive.ts
import { Directive, Input, OnDestroy, OnInit, TemplateRef, ViewContainerRef } from '@angular/core';
import { Subscription } from 'rxjs';
import { FeatureFlagService } from '../../core/services/feature-flag.service';

@Directive({
  selector: '[appFeatureFlag]',
  standalone: true
})
export class FeatureFlagDirective implements OnInit, OnDestroy {
  @Input('appFeatureFlag') featureName!: string;
  @Input('appFeatureFlagShowDisabled') showDisabled = false;

  private hasView = false;
  private subscription: Subscription | null = null;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private featureFlagService: FeatureFlagService
  ) {}

  ngOnInit(): void {
    if (!this.featureName) {
      console.error('Feature name is required for appFeatureFlag directive');
      return;
    }

    this.subscription = this.featureFlagService.isFeatureEnabled(this.featureName)
      .subscribe(isEnabled => {
        if (isEnabled && !this.hasView) {
          // Feature is enabled, show the element
          this.viewContainer.createEmbeddedView(this.templateRef);
          this.hasView = true;
        } else if (!isEnabled && this.hasView) {
          // Feature is disabled, remove the element
          this.viewContainer.clear();
          this.hasView = false;
        } else if (!isEnabled && !this.hasView && this.showDisabled) {
          // Feature is disabled but we want to show it as disabled
          this.viewContainer.createEmbeddedView(this.templateRef, {
            $implicit: { disabled: true }
          });
          this.hasView = true;
        }
      });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }
}
