
/* add-entry-dialog.component.scss (Revised with icon fix) */
:host {
  display: block;
}

.dialog-content {
  position: relative;
}

.entry-form {
  // Styles moved to ::ng-deep section
}

.date-field-container {
  ::ng-deep {
    .mat-mdc-form-field {
      .mat-mdc-form-field-suffix {
        .mat-datepicker-toggle, // Target the toggle itself
        .date-picker-toggle-fix { // Or your custom class if preferred
          position: relative;
          right: var(--spacing-sm); // Use theme variable
        }
      }
    }
  }
}


.loading-indicator {
  text-align: center;
  padding: 5px 10px;
  font-style: italic;
  color: var(--text-secondary-color); // Updated
  margin-right: 10px;
  font-size: 0.9rem;
}

.dialog-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  padding-top: var(--spacing-md); // Use theme variable
}

.error-message {
  color: var(--error-color);
  margin: var(--spacing-sm) 0; // Use theme variable (10px is between sm and md)
  padding: var(--spacing-sm) var(--spacing-md); // Use theme variables (8px 16px, or adjust if 12px is specific)
  border-radius: var(--border-radius-sm); 
  background-color: rgba(var(--error-color-rgb), 0.05); 
  font-size: 0.875rem;
}

.success-message {
  color: var(--success-color);
  margin: var(--spacing-sm) 0; // Use theme variable
  padding: var(--spacing-sm) var(--spacing-md); // Use theme variables
  border-radius: var(--border-radius-sm); 
  background-color: rgba(var(--success-color-rgb), 0.05); 
  font-size: var(--font-size-sm); // Match 0.875rem
}

.form-group {
  margin-bottom: var(--spacing-md); // Use theme variable

  &:first-of-type {
    margin-top: 0; // Keep as is
  }
}

.full-width {
  width: 100%;
}

.time-method-selector {
  margin-bottom: 20px;

  .time-method-label {
    display: block;
  margin-bottom: var(--spacing-sm); // Use theme variable
  color: var(--text-primary-color); 
  font-size: 0.9rem; // Keep as is or map to a variable if one fits
  font-weight: var(--font-weight-medium); 
  }

  mat-radio-button {
    margin-right: var(--spacing-lg); // Use theme variable (20px is between md and lg, lg is 24px)
  }
}

.custom-times {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md); // Use theme variable (15px is close to 16px)
}

.time-inputs-container {
  display: flex;
  gap: var(--spacing-md); // Use theme variable
  margin-bottom: var(--spacing-sm); // Use theme variable

  .time-field {
    flex: 1;
    width: 100%;
  }

  @media (max-width: 600px) {
    flex-direction: column;
    gap: var(--spacing-md); // Use theme variable
  }
}

.post-fields {
  display: flex;
  gap: var(--spacing-md); // Use theme variable
  margin-bottom: var(--spacing-md); // Use theme variable

  mat-form-field {
    flex: 1;
  }

  @media (max-width: 600px) {
    flex-direction: column;
    gap: var(--spacing-md); // Use theme variable
  }
}

/* Angular Material Timepicker styling - Scoped with ::ng-deep */
:host ::ng-deep {
  .mat-mdc-form-field {
    .mat-mdc-label {
      color: var(--text-secondary-color); // Updated
    }

    &.mat-focused {
      .mat-mdc-label {
        color: var(--primary-color); // Updated
      }
      .mat-mdc-form-field-outline-thick {
         color: var(--primary-color); // Updated
      }
    }
  }

  // Style the timepicker toggle
  .mat-timepicker-toggle {
    color: var(--primary-color); // Updated
    opacity: 0.7;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 1;
    }
  }

  // Style the timepicker panel
  .mat-timepicker-panel {
    background-color: var(--background-color); // Updated
    border-radius: var(--border-radius-sm); // Updated
    box-shadow: var(--shadow-dp4); // Updated, example elevation for popups

    .mat-timepicker-option {
      &.mat-selected {
        color: var(--primary-color); // Updated
        background-color: rgba(var(--primary-color-rgb), 0.1); // Subtle primary selection
      }

      &:hover:not(.mat-selected) {
        background-color: rgba(0, 0, 0, 0.04); // Standard Material hover
      }
    }
  }

  // Add spacing above the form
  .entry-form {
    padding-top: var(--spacing-md); // Use theme variable
  }
}

.checkbox-hint {
  small {
    font-size: var(--font-size-xs); // Updated
    color: var(--text-secondary-color); // Updated
    display: block;
    margin-top: 4px;
  }
}
