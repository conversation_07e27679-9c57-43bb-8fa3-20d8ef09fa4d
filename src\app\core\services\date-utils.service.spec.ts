// src/app/core/services/date-utils.service.spec.ts
import { TestBed } from '@angular/core/testing';
import { DateUtilsService } from './date-utils.service';

describe('DateUtilsService', () => {
  let service: DateUtilsService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(DateUtilsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('formatDate', () => {
    it('should format a date string correctly', () => {
      const result = service.formatDate('2025-07-04');
      // Format should be "Jul 4, 2025" (or similar depending on locale)
      expect(result).toContain('Jul');
      expect(result).toContain('4');
      expect(result).toContain('2025');
    });

    it('should handle empty input', () => {
      const result = service.formatDate('');
      expect(result).toBe('');
    });
  });

  describe('toDateString', () => {
    it('should convert a Date to YYYY-MM-DD format', () => {
      const date = new Date(2025, 6, 4); // July 4, 2025 (month is 0-indexed)
      const result = service.toDateString(date);
      expect(result).toBe('2025-07-04');
    });

    it('should handle null input', () => {
      const result = service.toDateString(null as any);
      expect(result).toBe('');
    });
  });

  describe('fromFormDate', () => {
    it('should create a Date from a form date string', () => {
      const result = service.fromFormDate('2025-07-04');
      expect(result.getFullYear()).toBe(2025);
      expect(result.getMonth()).toBe(6); // July is 6 (0-indexed)
      expect(result.getDate()).toBe(4);
    });

    it('should handle empty input', () => {
      const result = service.fromFormDate('');
      expect(result).toBeInstanceOf(Date);
    });
  });

  describe('isInDaylightSavingTime', () => {
    it('should identify summer date as DST', () => {
      const summerDate = new Date(2025, 6, 4); // July 4, 2025
      const result = service.isInDaylightSavingTime(summerDate);
      // This will depend on the local timezone, but in most places July is DST
      expect(result).toBe(true);
    });

    it('should identify winter date as not DST', () => {
      const winterDate = new Date(2025, 0, 4); // January 4, 2025
      const result = service.isInDaylightSavingTime(winterDate);
      // This will depend on the local timezone, but in most places January is not DST
      expect(result).toBe(false);
    });
  });
});
