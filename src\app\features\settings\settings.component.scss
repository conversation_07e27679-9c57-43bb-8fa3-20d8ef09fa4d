// src/app/features/settings/settings.component.scss

.settings-container {
  padding: var(--spacing-lg); // Use theme variable
  max-width: 800px;
  margin: 0 auto;
}

.settings-page-title {
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary-color); // Use theme variable (primary-color might be too strong for a title)
  text-align: center;
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xxl); // Use theme variable
  
  p {
    margin-top: var(--spacing-md);
    font-size: var(--font-size-lg);
    color: var(--text-secondary-color);
  }
}

// Styling for each <mat-card> section
.settings-section {
  margin-bottom: var(--spacing-lg); // Use theme variable

  // mat-card-title is styled by Material theme, override if necessary:
  // .mat-mdc-card-title {
  //   color: var(--primary-color);
  //   border-bottom: 1px solid var(--border-color);
  //   padding-bottom: var(--spacing-sm);
  //   font-size: var(--font-size-h3);
  // }

  .mat-mdc-card-content p.mat-body-1,
  .mat-mdc-card-content p { // General paragraph styling within cards
    margin-bottom: var(--spacing-sm); // Tighter spacing within cards
    font-size: var(--font-size-md);
    line-height: var(--line-height-base);
    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Contact Form Panel specific styling
.contact-form-panel {
  .mat-expansion-panel-header-title,
  .mat-expansion-panel-header-description {
    color: var(--text-primary-color); // Ensure text is readable
  }
  .section-description { // For the <p> tag below the header
    margin-bottom: var(--spacing-md);
    font-style: italic;
  }
}

.contact-form-material {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md); // Spacing between form fields

  .full-width-field {
    width: 100%;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end; // Align button to the right
    margin-top: var(--spacing-sm);
  }
}

// Server messages (Error/Success for contact form, general error)
.server-message {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  margin: var(--spacing-md) 0; // Margin above/below
  display: flex;
  align-items: center;
  
  mat-icon {
    margin-right: var(--spacing-sm);
  }

  &.error-message {
    background-color: rgba(var(--error-color-rgb), 0.1);
    color: var(--error-color);
    border: 1px solid rgba(var(--error-color-rgb), 0.3);
    mat-icon { color: var(--error-color); }
  }

  &.success-message {
    background-color: rgba(var(--success-color-rgb), 0.1);
    color: var(--success-color);
    border: 1px solid rgba(var(--success-color-rgb), 0.3);
     mat-icon { color: var(--success-color); }
  }
}
.general-error-message { // For the main page error if profile fails to load
  margin-top: var(--spacing-lg);
}


// Legal Links specific styling
.legal-links-card {
  .app-link { // Assuming global .app-link styles apply, or style 'a' directly
    color: var(--primary-color);
    text-decoration: none;
    &:hover {
      color: var(--accent-color);
      text-decoration: underline;
    }
  }
}

// Button specific styling
.full-width-button {
  width: 100%;
}

.button-spinner { // For spinners inside buttons
  margin-right: var(--spacing-sm);
  // Diameter is set on the element, color should be inherited or set to currentcolor
}

.logout-card {
  // Logout card might not need a header, content is just the button
  .mat-mdc-card-content {
    padding-top: var(--spacing-md) !important; // Adjust padding if no header
    padding-bottom: var(--spacing-md) !important;
  }
}

.no-profile-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary-color);
  text-align: center;
  mat-icon {
    font-size: var(--font-size-h1);
    margin-bottom: var(--spacing-md);
  }
}


/* Responsive adjustments */
@media (max-width: 768px) {
  .settings-container {
    padding: var(--spacing-md);
  }
  .settings-page-title {
    font-size: var(--font-size-h3);
  }
  .settings-section {
    // Mat-card already has responsive padding, adjust if needed
  }
  .contact-form-material .form-actions {
    // Buttons in forms might stack or become full-width on mobile
    // This is often handled by Material theming or default button behavior
  }
}
