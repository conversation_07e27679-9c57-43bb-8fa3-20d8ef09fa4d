// src/app/features/dashboard/overtime-summary/overtime-summary.component.ts
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { SupabaseService, TimeEntry, OtPeriod } from '../../../core/services/supabase.service';
import { User } from '@supabase/supabase-js';
// import { TooltipComponent } from '../../../shared/components/tooltip/tooltip.component'; // Removed
import { MatDialog } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip'; // Added
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { OvertimeCalculatorDialogComponent } from '../overtime-calculator/overtime-calculator-dialog.component';

@Component({
  selector: 'app-overtime-summary',
  standalone: true,
  imports: [
    CommonModule,
    // TooltipComponent, // Removed
    MatButtonModule,
    MatIconModule,
    MatTooltipModule, // Added
    MatCardModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './overtime-summary.component.html',
  styleUrls: ['./overtime-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OvertimeSummaryComponent implements OnInit, OnDestroy {
  currentUser: User | null = null;
  userSubscription: Subscription | null = null;

  isLoading = true;
  errorMessage: string | null = null;

  otPeriods: OtPeriod[] = [];
  currentOtPeriodIndex = 0;
  currentOtPeriod: OtPeriod | null = null;

  otHours = 0;
  mandatoryHoldCount = 0;
  voluntaryHoldCount = 0;

  constructor(
    private supabaseService: SupabaseService,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.userSubscription = this.supabaseService.currentUser$.subscribe(user => {
      if (user && typeof user !== 'boolean') {
        this.currentUser = user;
        this.loadOtPeriods();
      } else {
        this.currentUser = null;
        this.isLoading = false;
        this.cdr.markForCheck();
      }
    });
  }

  ngOnDestroy(): void {
    if (this.userSubscription) {
      this.userSubscription.unsubscribe();
    }
  }

  async loadOtPeriods(): Promise<void> {
    this.isLoading = true;
    this.errorMessage = null;
    this.cdr.markForCheck();

    try {
      // Get all OT periods
      this.otPeriods = await this.supabaseService.getAllOtPeriods();

      if (this.otPeriods.length > 0) {
        // Find the current OT period (the one that includes today's date)
        const today = new Date().toISOString().split('T')[0];
        const currentPeriodIndex = this.otPeriods.findIndex(period =>
          period.start_date <= today && period.end_date >= today
        );

        // If found, set it as the current period, otherwise use the most recent one
        this.currentOtPeriodIndex = currentPeriodIndex >= 0 ? currentPeriodIndex : 0;
        this.currentOtPeriod = this.otPeriods[this.currentOtPeriodIndex];

        // Load stats for the current period
        await this.loadOtPeriodStats();
      } else {
        this.currentOtPeriod = null;
        this.resetStats();
      }
    } catch (error: any) {
      console.error('Error loading OT periods:', error);
      this.errorMessage = error.message || 'Failed to load overtime periods.';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  async loadOtPeriodStats(): Promise<void> {
    if (!this.currentUser || !this.currentOtPeriod) {
      this.resetStats();
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;
    this.cdr.markForCheck();

    try {
      // Get time entries for the current OT period
      const timeEntries = await this.supabaseService.getTimeEntriesForUser(
        this.currentUser.id,
        this.currentOtPeriod.start_date,
        this.currentOtPeriod.end_date
      );

      // Calculate overtime stats
      this.calculateOtStats(timeEntries);
    } catch (error: any) {
      console.error('Error loading OT period stats:', error);
      this.errorMessage = error.message || 'Failed to load overtime statistics.';
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  calculateOtStats(entries: TimeEntry[]): void {
    let otHours = 0;
    let mandatoryHoldCount = 0;
    let voluntaryHoldCount = 0;

    entries.forEach(entry => {
      const startTime = new Date(entry.start_time);
      const endTime = new Date(entry.end_time);

      // Calculate duration in hours
      const durationMs = endTime.getTime() - startTime.getTime();
      const durationHours = durationMs / (1000 * 60 * 60);

      if (entry.entry_type === 'OT_VOLUNTARY') {
        otHours += durationHours;
        voluntaryHoldCount++;
      } else if (entry.entry_type === 'OT_MANDATED') {
        otHours += durationHours;
        // Only count towards mandatory if specified
        if (entry.counts_towards_mandatory) {
          mandatoryHoldCount++;
        }
      }
    });

    this.otHours = parseFloat(otHours.toFixed(2));
    this.mandatoryHoldCount = mandatoryHoldCount;
    this.voluntaryHoldCount = voluntaryHoldCount;
  }

  resetStats(): void {
    this.otHours = 0;
    this.mandatoryHoldCount = 0;
    this.voluntaryHoldCount = 0;
  }

  navigateToPreviousPeriod(): void {
    if (this.currentOtPeriodIndex < this.otPeriods.length - 1) {
      this.currentOtPeriodIndex++;
      this.currentOtPeriod = this.otPeriods[this.currentOtPeriodIndex];
      this.loadOtPeriodStats();
    }
  }

  navigateToNextPeriod(): void {
    if (this.currentOtPeriodIndex > 0) {
      this.currentOtPeriodIndex--;
      this.currentOtPeriod = this.otPeriods[this.currentOtPeriodIndex];
      this.loadOtPeriodStats();
    }
  }

  formatDateRange(startDate: string, endDate: string): string {
    const start = new Date(startDate + 'T00:00:00Z');
    const end = new Date(endDate + 'T00:00:00Z');

    const startMonth = start.toLocaleString('default', { month: 'short' });
    const endMonth = end.toLocaleString('default', { month: 'short' });

    const startDay = start.getDate();
    const endDay = end.getDate();

    const startYear = start.getFullYear();
    const endYear = end.getFullYear();

    if (startYear === endYear) {
      if (startMonth === endMonth) {
        return `${startMonth} ${startDay} - ${endDay}, ${startYear}`;
      } else {
        return `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${startYear}`;
      }
    } else {
      return `${startMonth} ${startDay}, ${startYear} - ${endMonth} ${endDay}, ${endYear}`;
    }
  }

  get canNavigateToPrevious(): boolean {
    return this.currentOtPeriodIndex < this.otPeriods.length - 1;
  }

  get canNavigateToNext(): boolean {
    return this.currentOtPeriodIndex > 0;
  }

  openOvertimeCalculator(): void {
    this.dialog.open(OvertimeCalculatorDialogComponent, {
      width: '600px',
      panelClass: 'custom-dialog-container'
    });
  }
}
