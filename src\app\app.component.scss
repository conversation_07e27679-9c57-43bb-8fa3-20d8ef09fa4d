// Main Sidenav Container
.app-sidenav-container {
  height: 100vh; // Full viewport height
}

// Sidenav (Sidebar)
.app-sidenav {
  width: 250px; // Standard sidebar width
  border-right: 1px solid var(--border-color); // Subtle border
  background-color: var(--primary-color); // Match sidebar component's bg
}

// Toolbar within Sidenav Content
.app-toolbar {
  position: sticky; // Make toolbar sticky
  top: 0;
  z-index: 100; // Ensure it's above scrolling content but below overlays if any
  // Height is typically 64px for desktop, 56px for mobile by Material spec.
  // MatToolbar handles this, but can be overridden if needed.
}

// Sidenav Toggle Button (within Toolbar)
.sidenav-toggle-button {
  @media (min-width: 769px) {
    display: none; // Hide toggle button on larger screens where sidenav is 'side'
  }
}

// Search Bar - sticky below the toolbar
.app-search-bar-sticky {
  position: sticky;
  top: 64px; // Height of the toolbar (adjust if toolbar height changes)
  z-index: 99; // Below toolbar, above content
  background-color: var(--surface-color); // Give it a background to not be transparent over content
  padding: var(--spacing-md);
  box-shadow: var(--shadow-dp1); // Optional: add a slight shadow to distinguish
}

// Main Content Area (for router-outlet)
.main-content-area {
  padding: var(--spacing-md); // Padding around the page content

  @media (max-width: 768px) {
    padding-bottom: calc(var(--spacing-md) + 60px); // Add space for the fixed mobile tab bar (60px height)
  }
}

// Sidenav Content Area (overall wrapper in mat-sidenav-content)
.app-sidenav-content {
  background-color: var(--surface-color); // Background for the main content area
}


/* Mobile Tab Bar Visibility Control */
.mobile-tab-bar {
  // The component itself (.tab-bar-nav in tab-bar.component.scss) handles fixed positioning.
  // Here we just control its display based on viewport.
  display: block; // Show by default (mobile-first)

  @media (min-width: 769px) {
    display: none; // Hide on desktop
  }
}

// Auth Container Styling
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--surface-color);
}

// Ensure router-outlet content itself doesn't have extra margins if it's not needed
// router-outlet + * can be too general, specific component styling is better.
// If components within router-outlet need specific layout, they should handle it.