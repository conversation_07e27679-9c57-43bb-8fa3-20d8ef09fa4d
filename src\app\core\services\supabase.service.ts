// src/app/core/services/supabase.service.ts
import { Injectable } from '@angular/core';
import {
  AuthChangeEvent, createClient, PostgrestSingleResponse, Session, SupabaseClient, User
} from '@supabase/supabase-js';
import { environment } from '../../../environments/environment';
import { BehaviorSubject } from 'rxjs';

// --- Interfaces ---
export interface Profile {
  id: string; username?: string; full_name?: string; avatar_url?: string;
  is_admin: boolean; subscription_status: string;
  initial_vacation_balance?: number | null; initial_sick_balance?: number | null; initial_annual_balance?: number | null;
  last_login_at?: string; created_at?: string; updated_at?: string;
}

export interface TimeEntry {
  id?: number; user_id: string; entry_type: string; start_time: string | Date;
  end_time: string | Date; post_number?: string; post_description?: string;
  notes?: string; created_at?: string; counts_towards_mandatory?: boolean;
}

export interface OtPeriod {
  id: number; start_date: string; end_date: string;
  display_name?: string;
  created_at: string; updated_at: string;
}

export interface HolidayEvent {
  id: number; event_date: string; event_name: string;
  created_at: string; updated_at?: string;
}

export interface PaydayDate {
  id: number; payday_date: string; pay_period_start: string; pay_period_end: string;
  created_at: string; updated_at: string;
}

export interface AppModule {
  id: number; module_name: string; display_name: string; description: string | null;
  is_enabled: boolean; access_level: string; depends_on: string[] | null;
  created_at: string; updated_at: string;
}

export interface AppStatistic {
  id: number; stat_name: string; stat_value: any;
  created_at: string; updated_at: string;
}

export interface ContactMessage {
  id: number; user_id: string | null; email: string; subject: string; message: string;
  is_read: boolean; created_at: string; updated_at: string;
}

@Injectable({ providedIn: 'root' })
export class SupabaseService {
  public supabaseInstance: SupabaseClient;
  private _currentUser = new BehaviorSubject<User | boolean | null>(null);
  private _currentSession = new BehaviorSubject<Session | null>(null);

  constructor() {
    // Create Supabase client with enhanced session persistence
    this.supabaseInstance = createClient(
      environment.supabaseUrl,
      environment.supabaseKey,
      {
        auth: {
          autoRefreshToken: true,
          persistSession: true,
          detectSessionInUrl: true,
          storageKey: 'etb_auth_token', // Custom storage key for better control
          storage: {
            getItem: (key) => {
              const value = localStorage.getItem(key);
              return value ? JSON.parse(value) : null;
            },
            setItem: (key, value) => {
              localStorage.setItem(key, JSON.stringify(value));
            },
            removeItem: (key) => {
              localStorage.removeItem(key);
            }
          },
          flowType: 'pkce' // Use PKCE flow for better security
        }
      }
    );

    // Enhanced auth state change listener with better session handling
    this.supabaseInstance.auth.onAuthStateChange(
      (event: AuthChangeEvent, session: Session | null) => {
        console.log(`Auth state changed: ${event}`);

        // Handle different auth events
        if (event === 'INITIAL_SESSION') {
          this._currentSession.next(session);
          this._currentUser.next(session?.user ?? false);
        }
        else if (event === 'SIGNED_IN') {
          this._currentSession.next(session);
          this._currentUser.next(session!.user);

          // Explicitly store session in localStorage for redundancy
          if (session) {
            localStorage.setItem('etb_user_session', JSON.stringify(session));
          }
        }
        else if (event === 'SIGNED_OUT') {
          this._currentSession.next(null);
          this._currentUser.next(false);
          localStorage.removeItem('etb_user_session');
        }
        else if (event === 'TOKEN_REFRESHED') {
          this._currentSession.next(session);

          // Update stored session when token is refreshed
          if (session) {
            localStorage.setItem('etb_user_session', JSON.stringify(session));
          }
        }
        else if (event === 'USER_UPDATED') {
          this._currentUser.next(session?.user ?? false);

          // Update stored session when user is updated
          if (session) {
            localStorage.setItem('etb_user_session', JSON.stringify(session));
          }
        }
      }
    );

    // Get the initial session with fallback to localStorage
    this.supabaseInstance.auth.getSession().then(({ data, error }) => {
      if (data?.session) {
        // Session from Supabase auth
        this._currentSession.next(data.session);
        this._currentUser.next(data.session.user);
      }
      else {
        // Try to recover session from localStorage
        const storedSession = localStorage.getItem('etb_user_session');
        if (storedSession) {
          try {
            const session = JSON.parse(storedSession);
            // Validate session by checking if it has a valid structure
            if (session && session.access_token && session.user) {
              this._currentSession.next(session);
              this._currentUser.next(session.user);

              // Refresh the token to ensure it's still valid
              this.supabaseInstance.auth.setSession({
                access_token: session.access_token,
                refresh_token: session.refresh_token
              }).catch(() => {
                // If refresh fails, clear the invalid session
                this._currentSession.next(null);
                this._currentUser.next(false);
                localStorage.removeItem('etb_user_session');
              });
            } else {
              this._currentUser.next(false);
            }
          } catch (e) {
            // Invalid stored session
            this._currentUser.next(false);
            localStorage.removeItem('etb_user_session');
          }
        } else {
          this._currentUser.next(false);
        }
      }
    }).catch(err => {
      console.error("Error in getSession:", err);
      this._currentUser.next(false);
    });
  }

  get auth() { return this.supabaseInstance.auth; }
  get currentUser$() { return this._currentUser.asObservable(); }
  get currentSession$() { return this._currentSession.asObservable(); }

  async getProfile(userId: string): Promise<Profile | null> {
     if (!userId) return null;
     const { data, error, status } = await this.supabaseInstance.from('profiles').select('*').eq('id', userId).single();
     if (error && status !== 406) { throw error; }
     return data as Profile | null;
  }
  async updateProfile(profileData: Partial<Profile> & { id: string }): Promise<Profile | null> {
    const { id, ...updates } = profileData;
    if (!id) throw new Error("User ID is required to update profile.");
    const { data, error } = await this.supabaseInstance.from('profiles').update(updates).eq('id', id).select().single();
    if (error) { throw error; }
    return data as Profile | null;
  }

  async getTimeEntriesForUser(userId: string, startDate: string, endDate: string): Promise<TimeEntry[]> {
     if (!userId) return [];
     let query = this.supabaseInstance.from('time_entries').select('*').eq('user_id', userId);
     if (startDate && startDate !== '1970-01-01') { query = query.gte('start_time', startDate); }
     if (endDate) { query = query.lte('start_time', `${endDate}T23:59:59.999Z`); }
     query = query.order('start_time', { ascending: true });
     const { data, error } = await query;
     if (error) { console.error('Error fetching time entries:', error); throw error; }
     return data || [];
   }
  async addTimeEntry(entry: Omit<TimeEntry, 'id' | 'created_at'>): Promise<TimeEntry | null> {
    if (!entry.user_id) { throw new Error("User ID is required to add a time entry."); }
    const entryToInsert = {
      ...entry,
      start_time: typeof entry.start_time === 'string' ? entry.start_time : entry.start_time.toISOString(),
      end_time: typeof entry.end_time === 'string' ? entry.end_time : entry.end_time.toISOString(),
      // Set counts_towards_mandatory to false by default if not provided and entry is OT_MANDATED
      counts_towards_mandatory: entry.entry_type === 'OT_MANDATED' ? (entry.counts_towards_mandatory ?? false) : undefined
    };
    const { data, error }: PostgrestSingleResponse<TimeEntry> = await this.supabaseInstance.from('time_entries').insert(entryToInsert).select().single();
    if (error) { console.error('[SERVICE] Error adding time entry:', error); throw error; }
    return data;
   }

  async updateTimeEntry(entry: Partial<TimeEntry> & { id: number }): Promise<TimeEntry | null> {
    const { id, ...updates } = entry;
    // Format dates if they are Date objects
    const formattedUpdates = {
      ...updates,
      start_time: updates.start_time ? (typeof updates.start_time === 'string' ? updates.start_time : updates.start_time.toISOString()) : undefined,
      end_time: updates.end_time ? (typeof updates.end_time === 'string' ? updates.end_time : updates.end_time.toISOString()) : undefined,
      // Only include counts_towards_mandatory if entry type is OT_MANDATED
      counts_towards_mandatory: updates.entry_type === 'OT_MANDATED' ? updates.counts_towards_mandatory : undefined
    };

    const { data, error } = await this.supabaseInstance.from('time_entries')
      .update(formattedUpdates)
      .eq('id', id)
      .select()
      .single();

    if (error) { console.error('[SERVICE] Error updating time entry:', error); throw error; }
    return data;
  }

  async deleteTimeEntry(id: number): Promise<void> {
    const { error } = await this.supabaseInstance.from('time_entries')
      .delete()
      .eq('id', id);

    if (error) { console.error('[SERVICE] Error deleting time entry:', error); throw error; }
  }
  async getCurrentOtPeriod(): Promise<OtPeriod | null> {
     const today = new Date().toISOString().split('T')[0];
     const { data, error }: PostgrestSingleResponse<OtPeriod> = await this.supabaseInstance.from('ot_periods').select('*').lte('start_date', today).gte('end_date', today).limit(1).single();
      if (error && error.code !== 'PGRST116') { console.error('[SERVICE] Error fetching current OT period:', error); throw error; }
     return data ?? null;
   }
  async getHolidaysEvents(startDate: string, endDate: string): Promise<HolidayEvent[]> {
      // Log the date range for debugging
      console.log('[SERVICE] Fetching holidays/events from', startDate, 'to', endDate);

      const { data, error } = await this.supabaseInstance.from('holidays_events')
        .select('*')
        .gte('event_date', startDate)
        .lte('event_date', endDate)
        .order('event_date', { ascending: true });

      if (error) {
        console.error('[SERVICE] Error fetching holidays/events:', error);
        throw error;
      }

      // Log the returned data for debugging
      console.log('[SERVICE] Holidays/events fetched successfully:', data);
      return data || [];
  }

  // Payday Dates methods
  async getPaydayDates(startDate: string, endDate: string): Promise<PaydayDate[]> {
    const { data, error } = await this.supabaseInstance.from('payday_dates')
      .select('*')
      .gte('payday_date', startDate)
      .lte('payday_date', endDate)
      .order('payday_date', { ascending: true });

    if (error) { console.error('[SERVICE] Error fetching payday dates:', error); throw error; }
    return data || [];
  }

  async addPaydayDate(paydayDate: Omit<PaydayDate, 'id' | 'created_at' | 'updated_at'>): Promise<PaydayDate | null> {
    const { data, error } = await this.supabaseInstance.from('payday_dates')
      .insert(paydayDate)
      .select()
      .single();

    if (error) { console.error('[SERVICE] Error adding payday date:', error); throw error; }
    return data;
  }

  async updatePaydayDate(paydayDate: Partial<PaydayDate> & { id: number }): Promise<PaydayDate | null> {
    const { id, ...updates } = paydayDate;
    const { data, error } = await this.supabaseInstance.from('payday_dates')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) { console.error('[SERVICE] Error updating payday date:', error); throw error; }
    return data;
  }

  async deletePaydayDate(id: number): Promise<void> {
    const { error } = await this.supabaseInstance.from('payday_dates')
      .delete()
      .eq('id', id);

    if (error) { console.error('[SERVICE] Error deleting payday date:', error); throw error; }
  }

  // OT Periods methods
  async getAllOtPeriods(): Promise<OtPeriod[]> {
    const { data, error } = await this.supabaseInstance.from('ot_periods')
      .select('*')
      .order('start_date', { ascending: false });

    if (error) { console.error('[SERVICE] Error fetching OT periods:', error); throw error; }
    return data || [];
  }

  async addOtPeriod(otPeriod: Omit<OtPeriod, 'id' | 'created_at' | 'updated_at'>): Promise<OtPeriod | null> {
    const { data, error } = await this.supabaseInstance.from('ot_periods')
      .insert(otPeriod)
      .select()
      .single();

    if (error) { console.error('[SERVICE] Error adding OT period:', error); throw error; }
    return data;
  }

  async updateOtPeriod(otPeriod: Partial<OtPeriod> & { id: number }): Promise<OtPeriod | null> {
    const { id, ...updates } = otPeriod;
    const { data, error } = await this.supabaseInstance.from('ot_periods')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) { console.error('[SERVICE] Error updating OT period:', error); throw error; }
    return data;
  }

  async deleteOtPeriod(id: number): Promise<void> {
    const { error } = await this.supabaseInstance.from('ot_periods')
      .delete()
      .eq('id', id);

    if (error) { console.error('[SERVICE] Error deleting OT period:', error); throw error; }
  }

  // Holidays/Events methods
  async addHolidayEvent(holidayEvent: Omit<HolidayEvent, 'id' | 'created_at' | 'updated_at'>): Promise<HolidayEvent | null> {
    // Log the event date for debugging
    console.log('[SERVICE] Adding holiday/event with date:', holidayEvent.event_date);

    // Ensure we're using the date string directly without any conversion
    const { data, error } = await this.supabaseInstance.from('holidays_events')
      .insert(holidayEvent)
      .select()
      .single();

    if (error) {
      console.error('[SERVICE] Error adding holiday/event:', error);
      throw error;
    }

    // Log the returned data for debugging
    console.log('[SERVICE] Holiday/event added successfully:', data);
    return data;
  }

  async updateHolidayEvent(holidayEvent: Partial<HolidayEvent> & { id: number }): Promise<HolidayEvent | null> {
    // Log the event date for debugging
    console.log('[SERVICE] Updating holiday/event with date:', holidayEvent.event_date);

    const { id, ...updates } = holidayEvent;

    // Ensure we're using the date string directly without any conversion
    const { data, error } = await this.supabaseInstance.from('holidays_events')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('[SERVICE] Error updating holiday/event:', error);
      throw error;
    }

    // Log the returned data for debugging
    console.log('[SERVICE] Holiday/event updated successfully:', data);
    return data;
  }

  async deleteHolidayEvent(id: number): Promise<void> {
    const { error } = await this.supabaseInstance.from('holidays_events')
      .delete()
      .eq('id', id);

    if (error) { console.error('[SERVICE] Error deleting holiday/event:', error); throw error; }
  }

  // App Statistics methods
  async getAppStatistics(): Promise<AppStatistic[]> {
    try {
      // First, update the statistics to ensure they're current
      await this.updateAppStatistics();

      // Then fetch the updated statistics
      const { data, error } = await this.supabaseInstance.from('app_statistics')
        .select('*');

      if (error) { throw error; }
      return data || [];
    } catch (error) {
      console.error('[SERVICE] Error fetching app statistics:', error);
      throw error;
    }
  }

  private async updateAppStatistics(): Promise<void> {
    try {
      // Update total users count
      const { count: userCount, error: userError } = await this.supabaseInstance
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      if (userError) throw userError;

      // Update total entries count
      const { count: entryCount, error: entryError } = await this.supabaseInstance
        .from('time_entries')
        .select('*', { count: 'exact', head: true });

      if (entryError) throw entryError;

      // Get current month for monthly active users
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth();

      // Get active users for current month (users who have logged in this month)
      const startOfMonth = new Date(currentYear, currentMonth, 1).toISOString();
      const endOfMonth = new Date(currentYear, currentMonth + 1, 0, 23, 59, 59, 999).toISOString();

      const { count: activeUserCount, error: activeUserError } = await this.supabaseInstance
        .from('profiles')
        .select('*', { count: 'exact', head: true })
        .gte('last_login_at', startOfMonth)
        .lte('last_login_at', endOfMonth);

      if (activeUserError) throw activeUserError;

      // Prepare monthly active users data structure
      const monthlyData: any = {};
      if (!monthlyData[currentYear]) {
        monthlyData[currentYear] = {};
      }
      monthlyData[currentYear][currentMonth] = activeUserCount || 0;

      // Update statistics in database
      await this.supabaseInstance.from('app_statistics')
        .upsert([
          {
            stat_name: 'total_users',
            stat_value: { count: userCount || 0 }
          },
          {
            stat_name: 'total_entries',
            stat_value: { count: entryCount || 0 }
          },
          {
            stat_name: 'monthly_active_users',
            stat_value: { data: monthlyData }
          }
        ], { onConflict: 'stat_name' });

    } catch (error) {
      console.error('[SERVICE] Error updating app statistics:', error);
      // Don't throw here to prevent blocking the getAppStatistics method
    }
  }

  // Contact Messages methods
  async submitContactMessage(message: Omit<ContactMessage, 'id' | 'is_read' | 'created_at' | 'updated_at'>): Promise<ContactMessage | null> {
    const { data, error } = await this.supabaseInstance.from('contact_messages')
      .insert({ ...message, is_read: false })
      .select()
      .single();

    if (error) { console.error('[SERVICE] Error submitting contact message:', error); throw error; }
    return data;
  }

  async getContactMessages(): Promise<ContactMessage[]> {
    const { data, error } = await this.supabaseInstance.from('contact_messages')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) { console.error('[SERVICE] Error fetching contact messages:', error); throw error; }
    return data || [];
  }

  async markContactMessageAsRead(id: number): Promise<void> {
    const { error } = await this.supabaseInstance.from('contact_messages')
      .update({ is_read: true })
      .eq('id', id);

    if (error) { console.error('[SERVICE] Error marking contact message as read:', error); throw error; }
  }
}