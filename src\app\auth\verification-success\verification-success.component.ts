// src/app/auth/verification-success/verification-success.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { SupabaseService } from '../../core/services/supabase.service';

@Component({
  selector: 'app-verification-success',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './verification-success.component.html',
  styleUrls: ['./verification-success.component.scss']
})
export class VerificationSuccessComponent implements OnInit {
  isLoading = true;
  message = 'Verifying your email...';
  isSuccess = false;

  constructor(
    private supabaseService: SupabaseService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    // Supabase automatically handles the verification when the page loads
    // We just need to check if there are any error parameters

    // Check for error parameters in both query string and hash fragment
    const queryParams = new URLSearchParams(window.location.search);
    const hashParams = new URLSearchParams(window.location.hash.replace('#', ''));

    const hasError = queryParams.has('error') || hashParams.has('error');
    const errorCode = queryParams.get('error_code') || hashParams.get('error_code');
    const errorDescription = queryParams.get('error_description') || hashParams.get('error_description');

    if (hasError) {
      this.handleVerificationError(errorCode, errorDescription);
    } else {
      // No error in URL, check if verification was successful
      this.checkVerificationStatus();
    }
  }

  private checkVerificationStatus(): void {
    // First check if we have a session, which would indicate successful verification
    this.supabaseService.currentSession$.subscribe(session => {
      if (session) {
        this.isSuccess = true;
        this.message = 'Your email has been verified successfully!';
        this.isLoading = false;

        // After a short delay, redirect to dashboard or login
        setTimeout(() => {
          this.router.navigate(['/dashboard']);
        }, 3000);
      } else {
        // If no session, we need to wait for Supabase to process the verification
        // This is handled by Supabase's auth state change listener
        // We'll set a timeout to check again after a short delay
        setTimeout(() => {
          this.supabaseService.auth.getSession().then(({ data }) => {
            if (data?.session) {
              this.isSuccess = true;
              this.message = 'Your email has been verified successfully!';

              // After a short delay, redirect to dashboard
              setTimeout(() => {
                this.router.navigate(['/dashboard']);
              }, 2000);
            } else {
              // Still no session, try to extract token from URL and verify manually
              this.tryManualVerification();
            }
            this.isLoading = false;
          });
        }, 1000);
      }
    });
  }

  private tryManualVerification(): void {
    // This is a fallback method to try to handle the verification manually
    // It may not be needed if Supabase's auto-detection works correctly

    // Check if we have a token in the URL (either in query params or hash)
    const fullUrl = window.location.href;

    if (fullUrl.includes('access_token=') || fullUrl.includes('token=')) {
      this.isSuccess = true;
      this.message = 'Your email has been verified! You will be redirected to login.';

      // After a short delay, redirect to login
      setTimeout(() => {
        this.router.navigate(['/auth/login']);
      }, 3000);
    } else {
      this.isSuccess = false;
      this.message = 'Email verification could not be completed. Please try the verification link again or request a new verification email.';
    }
  }

  private handleVerificationError(errorCode: string | null, errorDescription: string | null): void {

    this.isSuccess = false;

    // Set appropriate error message based on the error code
    if (errorCode === 'otp_expired') {
      this.message = 'The verification link has expired. Please request a new verification email.';
    } else if (errorCode === 'access_denied') {
      this.message = 'Access denied. The verification link may be invalid or has already been used.';
    } else {
      this.message = errorDescription || 'Email verification failed. Please try signing up again or request a new verification email.';
    }

    this.isLoading = false;
  }

  navigateToLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  navigateToDashboard(): void {
    this.router.navigate(['/dashboard']);
  }

  resendVerificationEmail(): void {
    this.router.navigate(['/auth/login'], { queryParams: { resendVerification: 'true' } });
  }
}
