<div class="login-page-container">
  <mat-card class="login-card">
    <mat-card-header>
      <mat-card-title class="login-title">Login</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline" class="full-width-field">
          <mat-label>Email</mat-label>
          <input matInput formControlName="email" type="email" placeholder="<EMAIL>" required>
          <mat-error *ngIf="email?.invalid && (email?.dirty || email?.touched) && email?.errors?.['required']">
            Email is required.
          </mat-error>
          <mat-error *ngIf="email?.invalid && (email?.dirty || email?.touched) && email?.errors?.['email']">
            Please enter a valid email.
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width-field">
          <mat-label>Password</mat-label>
          <input matInput formControlName="password" type="password" placeholder="Password" required>
          <mat-error *ngIf="password?.invalid && (password?.dirty || password?.touched) && password?.errors?.['required']">
            Password is required.
          </mat-error>
        </mat-form-field>

        <div *ngIf="errorMessage" class="server-message error-message">
          <mat-icon>error_outline</mat-icon>
          <span>{{ errorMessage }}</span>
          <button *ngIf="showResendButton" mat-stroked-button color="primary" type="button" (click)="resendVerificationEmail()" class="resend-button">
            Resend Verification Email
          </button>
        </div>

        <div *ngIf="successMessage" class="server-message success-message">
          <mat-icon>check_circle_outline</mat-icon>
          <span>{{ successMessage }}</span>
        </div>

        <button mat-raised-button color="primary" type="submit" [disabled]="loginForm.invalid || isLoading" class="full-width-button login-submit-button">
          <mat-progress-spinner *ngIf="isLoading" mode="indeterminate" diameter="20" class="button-spinner"></mat-progress-spinner>
          {{ isLoading ? 'Logging in...' : 'Login' }}
        </button>
      </form>
    </mat-card-content>
    <mat-card-actions class="login-card-actions">
      <p>
        Don't have an account? <a routerLink="/auth/signup" class="nav-link">Sign Up</a>
      </p>
    </mat-card-actions>
  </mat-card>
</div>