// src/app/features/dashboard/pay-period-stats/view-998-format-dialog.component.scss

// Styles for the dialog content.
// Using :host to ensure these styles are applied to the component's host element,
// which is standard for component-specific dialog styling that isn't part of the global theme.
:host {
  mat-dialog-content {
    min-height: 200px; // Keep specific min-height for this dialog's content area
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center; // Ensure text within is centered if it wraps
    color: var(--text-secondary-color); // Use theme variable for text color
    font-size: var(--font-size-md); // Use theme variable for font size
  }

  // Add padding to the dialog title and actions if needed,
  // though these are generally handled well by Angular Material's theming.
  // h2[mat-dialog-title] {
  //   color: var(--primary-color); // Example
  // }
}
