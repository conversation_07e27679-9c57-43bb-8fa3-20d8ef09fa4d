// src/app/features/dashboard/dashboard.component.scss

.dashboard-page-container {
  padding: var(--spacing-lg); // e.g., 24px
  margin: 0 auto;
  max-width: 1200px; // Or your preferred max-width
}

.dashboard-header {
  margin-bottom: var(--spacing-lg);
}

.dashboard-title {
  font-size: var(--font-size-h2); // Using defined H2 size
  font-weight: var(--font-weight-bold);
  color: var(--text-primary-color); // Use primary text color
}

.dashboard-loader {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xxl); // e.g., 48px
  margin-bottom: var(--spacing-lg);
  background-color: var(--background-color); // Card-like background
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-dp1);

  p {
    margin-top: var(--spacing-md);
    font-size: var(--font-size-lg);
    color: var(--text-secondary-color);
  }
}

.dashboard-alert {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-dp1);
  border-left-width: 4px;
  border-left-style: solid;

  mat-icon {
    margin-right: var(--spacing-sm);
  }

  span {
    font-size: var(--font-size-md);
  }

  &.error-alert {
    background-color: rgba(var(--error-color-rgb), 0.1);
    border-left-color: var(--error-color);
    color: var(--error-color);
    mat-icon {
      color: var(--error-color);
    }
  }
}

// Container for app-pay-period-stats and app-overtime-summary
// These components will be styled as cards themselves.
// Add margin between these child components if not handled internally by them.
app-pay-period-stats,
app-overtime-summary {
  display: block; // Ensure they take block layout
  margin-bottom: var(--spacing-lg);
}


.dashboard-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md); // For spacing between buttons
  margin-top: var(--spacing-lg); // Space above the action buttons

  button { // Target mat-stroked-button specifically if needed
    mat-icon {
      margin-right: var(--spacing-xs); // Space between icon and text in button
    }
  }
}

// Responsive adjustments if needed
@media (max-width: 768px) {
  .dashboard-page-container {
    padding: var(--spacing-md); // Smaller padding on mobile
  }

  .dashboard-title {
    font-size: var(--font-size-h3); // Slightly smaller title on mobile
  }

  .dashboard-actions {
    flex-direction: column; // Stack buttons on smaller screens
    align-items: stretch; // Make buttons full width

    button {
      width: 100%;
      margin-bottom: var(--spacing-sm); // Add some space between stacked buttons
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}