import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-view-998-format-dialog',
  standalone: true,
  imports: [CommonModule, MatDialogModule, MatButtonModule],
  template: `
    <h2 mat-dialog-title>View in 998 Format</h2>
    <mat-dialog-content>
      <p>998 Format view will be implemented in a future update.</p>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button mat-dialog-close>Close</button>
    </mat-dialog-actions>
  `,
  styleUrls: ['./view-998-format-dialog.component.scss']
})
export class View998FormatDialogComponent {
  constructor(public dialogRef: MatDialogRef<View998FormatDialogComponent>) {}
}
