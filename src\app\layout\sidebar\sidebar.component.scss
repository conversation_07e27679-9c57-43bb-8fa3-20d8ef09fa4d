// Styles for the content within mat-sidenav
.sidebar-content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%; // Fill the sidenav
  background-color: var(--primary-color);
  color: var(--on-primary-color);
}

.logo {
  padding: var(--spacing-lg);
  text-align: center;
  border-bottom: 1px solid rgba(var(--on-primary-color-rgb), 0.12); // Slightly adjusted opacity

  h2 {
    margin: 0;
    font-size: var(--font-size-h4); // Using defined variable
    font-weight: var(--font-weight-bold);
    letter-spacing: 1px;
    color: var(--on-primary-color);
  }
}

.nav-menu { // Targets mat-nav-list
  flex: 1; // Allow it to grow and push footer down
  padding: var(--spacing-sm) 0; // Adjusted padding

  .mat-mdc-list-item { // Target Material list items
    color: var(--on-primary-color);
    padding: 0 var(--spacing-lg) !important; // Override default padding for consistency
    height: 48px !important; // Consistent item height

    .mat-icon { // Target mat-icon within list items
      color: var(--on-primary-color);
      margin-right: var(--spacing-md);
    }

    span { // Target span (label) within list items
      font-size: var(--font-size-md);
    }

    &:hover {
      background-color: rgba(var(--on-primary-color-rgb), 0.08); // Subtle hover
    }

    &.active-link {
      background-color: rgba(var(--on-primary-color-rgb), 0.15); // Slightly more prominent active state
      border-left: 4px solid var(--accent-color);
      padding-left: calc(var(--spacing-lg) - 4px) !important; // Adjust padding for border

      .mat-icon, span {
        color: var(--on-primary-color); // Ensure text is still contrasting
        font-weight: var(--font-weight-medium);
      }
    }
  }
}

.sidebar-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: center;
  font-size: var(--font-size-sm);
  border-top: 1px solid rgba(var(--on-primary-color-rgb), 0.12);

  p {
    margin: 0;
    opacity: 0.8; // Slightly increased opacity
    color: var(--on-primary-color);
  }
}

// Note: The media query for hiding .sidebar is removed as mat-sidenav handles responsiveness.
