<mat-card class="pay-period-stats-card">
  <mat-card-content>
    <div class="summary-header">
      <h3 class="summary-title">
        Pay Period Summary
        <mat-icon
          class="summary-title-tooltip"
          matTooltip="A pay period is a calendar month from the 1st to the last day of the month"
          aria-label="Pay period definition tooltip">info_outline</mat-icon>
      </h3>
      <div class="summary-nav">
        <button mat-icon-button color="primary" (click)="navigateToPreviousPeriod()" [disabled]="currentPayPeriodIndex >= payPeriods.length - 1 || isLoading" aria-label="Previous period">
          <mat-icon>chevron_left</mat-icon>
        </button>
        <span class="summary-nav-label">Pay Period</span>
        <button mat-icon-button color="primary" (click)="navigateToNextPeriod()" [disabled]="currentPayPeriodIndex <= 0 || isLoading" aria-label="Next period">
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
    </div>

    <div *ngIf="currentPayPeriod" class="period-indicator">
      <span class="period-indicator-text">
        {{ formatDateRange(currentPayPeriod.pay_period_start, currentPayPeriod.pay_period_end) }}
      </span>
    </div>
    
    <div *ngIf="isLoading" class="summary-loader">
      <mat-progress-spinner mode="indeterminate" diameter="30"></mat-progress-spinner>
      <span>Loading statistics...</span>
    </div>

    <div *ngIf="errorMessage" class="summary-alert error-alert">
      <mat-icon>error_outline</mat-icon>
      <span>{{ errorMessage }}</span>
    </div>

    <div *ngIf="!isLoading && !errorMessage" class="stats-container">
      <div class="stats-grid main-stats-grid">
        <div class="stat-item">
          <div class="stat-label">
            Regular Hours
            <mat-icon
              class="stat-tooltip"
              matTooltip="Total regular work hours logged during this pay period (excludes overtime and leave)"
              aria-label="Regular hours tooltip">info_outline</mat-icon>
          </div>
          <div class="stat-value">{{ regularHours }} hrs</div>
        </div>

        <div class="stat-item">
          <div class="stat-label">
            Total Hours
            <mat-icon
              class="stat-tooltip"
              matTooltip="Total hours logged during this pay period (regular work + all leave types)"
              aria-label="Total hours tooltip">info_outline</mat-icon>
          </div>
          <div class="stat-value">{{ totalHours }} hrs</div>
        </div>
      </div>

      <!-- Leave Usage Section -->
      <div class="leave-usage-section">
        <h4 class="leave-usage-title">
          Leave Usage
          <mat-icon
            class="stat-tooltip"
            matTooltip="Hours of leave used during this pay period"
            aria-label="Leave usage tooltip">info_outline</mat-icon>
        </h4>
        <div class="stats-grid leave-stats-grid">
          <div class="stat-item">
            <div class="stat-label">
              Vacation
              <mat-icon
                class="stat-tooltip"
                matTooltip="Total vacation leave hours used during this pay period"
                aria-label="Vacation leave tooltip">info_outline</mat-icon>
            </div>
            <div class="stat-value">{{ vacationHours }} hrs</div>
          </div>

          <div class="stat-item">
            <div class="stat-label">
              Sick
              <mat-icon
                class="stat-tooltip"
                matTooltip="Total sick leave hours used during this pay period"
                aria-label="Sick leave tooltip">info_outline</mat-icon>
            </div>
            <div class="stat-value">{{ sickHours }} hrs</div>
          </div>

          <div class="stat-item">
            <div class="stat-label">
              Annual Leave
              <mat-icon
                class="stat-tooltip"
                matTooltip="Total annual leave hours used during this pay period"
                aria-label="Annual leave tooltip">info_outline</mat-icon>
            </div>
            <div class="stat-value">{{ annualLeaveHours }} hrs</div>
          </div>
        </div>
      </div>
    </div>
     <p *ngIf="!currentPayPeriod && !isLoading && !errorMessage" class="no-data-message">
      No pay periods available or defined.
    </p>
  </mat-card-content>
</mat-card>
