// src/app/features/dashboard/pay-period-stats/pay-period-stats.component.ts
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { SupabaseService, TimeEntry, PaydayDate } from '../../../core/services/supabase.service';
import { DateUtilsService } from '../../../core/services/date-utils.service';
import { User } from '@supabase/supabase-js';
// import { TooltipComponent } from '../../../shared/components/tooltip/tooltip.component'; // Removed
import { MatDialog } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip'; // Added
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { View998FormatDialogComponent } from './view-998-format-dialog.component';

@Component({
  selector: 'app-pay-period-stats',
  standalone: true,
  imports: [
    CommonModule,
    // TooltipComponent, // Removed
    MatButtonModule,
    MatIconModule,
    MatTooltipModule, // Added
    MatCardModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './pay-period-stats.component.html',
  styleUrls: ['./pay-period-stats.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PayPeriodStatsComponent implements OnInit, OnDestroy {
  currentUser: User | null = null;
  userSubscription: Subscription | null = null;

  isLoading = true;
  errorMessage: string | null = null;

  currentPayPeriod: PaydayDate | null = null;
  payPeriods: PaydayDate[] = [];
  currentPayPeriodIndex = 0;

  regularHours = 0;
  vacationHours = 0;
  sickHours = 0;
  annualLeaveHours = 0;
  totalHours = 0;

  constructor(
    private supabaseService: SupabaseService,
    private dateUtils: DateUtilsService,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.userSubscription = this.supabaseService.currentUser$.subscribe(user => {
      if (user && typeof user !== 'boolean') {
        this.currentUser = user;
        this.loadPayPeriods();
      } else {
        this.currentUser = null;
        this.isLoading = false;
        this.cdr.markForCheck();
      }
    });
  }

  ngOnDestroy(): void {
    this.userSubscription?.unsubscribe();
  }

  async loadPayPeriods(): Promise<void> {
    this.isLoading = true;
    this.errorMessage = null;
    this.cdr.markForCheck();

    try {
      // Get current date in California time zone
      const today = this.dateUtils.getCurrentCaliforniaDate();
      const currentYear = today.getFullYear();
      const currentMonth = today.getMonth();

      // Get all pay periods from database (we'll need them for navigation)
      const dbPayPeriods = await this.supabaseService.getPaydayDates('2020-01-01', '2030-12-31');

      // Create a list of all months as pay periods (from 2020 to 2030)
      const allPayPeriods: PaydayDate[] = [];

      // Generate pay periods for all months if none exist in the database
      for (let year = 2020; year <= 2030; year++) {
        for (let month = 0; month < 12; month++) {
          // First day of month (1st)
          const startDate = new Date(year, month, 1);
          // Last day of month (28th, 29th, 30th, or 31st depending on month)
          const endDate = new Date(year, month + 1, 0);

          // Format dates as YYYY-MM-DD strings using DateUtilsService
          const startDateStr = this.dateUtils.toDateString(startDate);
          const endDateStr = this.dateUtils.toDateString(endDate);

          console.log(`Generated pay period for ${year}-${month+1}: ${startDateStr} to ${endDateStr}`);

          // Default payday is the last day of the month
          const paydayDateStr = endDateStr;

          // Check if this pay period exists in the database
          const existingPeriod = dbPayPeriods.find(p =>
            p.pay_period_start === startDateStr && p.pay_period_end === endDateStr
          );

          if (existingPeriod) {
            // Use the existing period from the database
            allPayPeriods.push(existingPeriod);
          } else {
            // Create a synthetic pay period
            allPayPeriods.push({
              id: -1 * (year * 100 + month + 1), // Negative ID to avoid conflicts with DB IDs
              payday_date: paydayDateStr,
              pay_period_start: startDateStr,
              pay_period_end: endDateStr,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
          }
        }
      }

      // Sort pay periods by date (newest first)
      this.payPeriods = allPayPeriods.sort((a, b) =>
        new Date(b.pay_period_start).getTime() - new Date(a.pay_period_start).getTime()
      );

      // Find the current pay period (current month)
      const currentMonthStart = this.dateUtils.toDateString(new Date(currentYear, currentMonth, 1));
      const currentMonthEnd = this.dateUtils.toDateString(new Date(currentYear, currentMonth + 1, 0));

      console.log('Current month period:', currentMonthStart, 'to', currentMonthEnd);

      const currentPayPeriod = this.payPeriods.find(period =>
        period.pay_period_start === currentMonthStart && period.pay_period_end === currentMonthEnd
      );

      if (currentPayPeriod) {
        this.currentPayPeriod = currentPayPeriod;
        this.currentPayPeriodIndex = this.payPeriods.findIndex(p =>
          p.pay_period_start === currentPayPeriod.pay_period_start &&
          p.pay_period_end === currentPayPeriod.pay_period_end
        );
        console.log('Found current pay period:', this.currentPayPeriod, 'at index', this.currentPayPeriodIndex);
      } else {
        // If no current pay period, use the most recent one
        this.currentPayPeriod = this.payPeriods[0];
        this.currentPayPeriodIndex = 0;
        console.log('Using most recent pay period:', this.currentPayPeriod);
      }

      // Enable navigation buttons
      this.cdr.markForCheck();

      // Load stats for the current pay period
      await this.loadPayPeriodStats();

    } catch (error: any) {
      console.error('Error loading pay periods:', error);
      this.errorMessage = error.message || 'Failed to load pay periods.';
      this.cdr.markForCheck();
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  async loadPayPeriodStats(): Promise<void> {
    if (!this.currentUser || !this.currentPayPeriod) return;

    this.isLoading = true;
    this.errorMessage = null;
    this.cdr.markForCheck();

    try {
      // Get time entries for the current pay period
      const timeEntries = await this.supabaseService.getTimeEntriesForUser(
        this.currentUser.id,
        this.currentPayPeriod.pay_period_start,
        this.currentPayPeriod.pay_period_end
      );

      // Calculate hours
      this.calculateHours(timeEntries);

    } catch (error: any) {
      console.error('Error loading pay period stats:', error);
      this.errorMessage = error.message || 'Failed to load pay period statistics.';
      this.cdr.markForCheck();
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }

  calculateHours(entries: TimeEntry[]): void {
    let regularHours = 0;
    let vacationHours = 0;
    let sickHours = 0;
    let annualLeaveHours = 0;

    entries.forEach(entry => {
      const startTime = new Date(entry.start_time);
      const endTime = new Date(entry.end_time);

      // Calculate duration in hours
      const durationMs = endTime.getTime() - startTime.getTime();
      const durationHours = durationMs / (1000 * 60 * 60);

      // Categorize hours by entry type
      switch (entry.entry_type) {
        case 'REGULAR_WORK':
          regularHours += durationHours;
          break;
        case 'VACATION':
          vacationHours += durationHours;
          break;
        case 'SICK':
          sickHours += durationHours;
          break;
        case 'ANNUAL_LEAVE':
          annualLeaveHours += durationHours;
          break;
        // Overtime hours are not included in this component as requested
      }
    });

    // Format to 2 decimal places
    this.regularHours = parseFloat(regularHours.toFixed(2));
    this.vacationHours = parseFloat(vacationHours.toFixed(2));
    this.sickHours = parseFloat(sickHours.toFixed(2));
    this.annualLeaveHours = parseFloat(annualLeaveHours.toFixed(2));

    // Calculate total hours (regular + leave hours)
    this.totalHours = parseFloat((regularHours + vacationHours + sickHours + annualLeaveHours).toFixed(2));
  }

  navigateToPreviousPeriod(): void {
    if (this.currentPayPeriodIndex < this.payPeriods.length - 1) {
      this.currentPayPeriodIndex++;
      this.currentPayPeriod = this.payPeriods[this.currentPayPeriodIndex];
      console.log('Navigating to previous period:', this.currentPayPeriod);
      this.loadPayPeriodStats();
      this.cdr.markForCheck();
    }
  }

  navigateToNextPeriod(): void {
    if (this.currentPayPeriodIndex > 0) {
      this.currentPayPeriodIndex--;
      this.currentPayPeriod = this.payPeriods[this.currentPayPeriodIndex];
      console.log('Navigating to next period:', this.currentPayPeriod);
      this.loadPayPeriodStats();
      this.cdr.markForCheck();
    }
  }

  formatDateRange(startDate: string, endDate: string): string {
    // Use DateUtilsService to create dates from the date strings
    const startDateObj = this.dateUtils.fromFormDate(startDate);
    const endDateObj = this.dateUtils.fromFormDate(endDate);

    // Extract date components
    const startYear = startDateObj.getFullYear();
    const startMonth = startDateObj.getMonth(); // 0-based month
    const startDay = startDateObj.getDate();

    const endYear = endDateObj.getFullYear();
    const endMonth = endDateObj.getMonth(); // 0-based month
    const endDay = endDateObj.getDate();

    // Format as "Month YYYY" (e.g., "May 2025")
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                        'July', 'August', 'September', 'October', 'November', 'December'];

    // Check if this is a full month (1st day to last day of month)
    const isFirstDayOfMonth = startDay === 1;
    const isLastDayOfMonth = endDay === new Date(endYear, endMonth + 1, 0).getDate();

    // Check if start and end are in the same month
    const isSameMonth = startMonth === endMonth && startYear === endYear;

    if (isFirstDayOfMonth && isLastDayOfMonth && isSameMonth) {
      // Full month in the same month - show just the month name
      return `${monthNames[startMonth]} ${startYear}`;
    } else {
      // Show the full date range in MM/DD/YYYY format
      const formatDate = (year: number, month: number, day: number) => {
        return `${month + 1}/${day}/${year}`;
      };

      return `${formatDate(startYear, startMonth, startDay)} - ${formatDate(endYear, endMonth, endDay)}`;
    }
  }

  openView998FormatDialog(): void {
    this.dialog.open(View998FormatDialogComponent, {
      width: '600px',
      panelClass: 'custom-dialog-container'
    });
  }
}
