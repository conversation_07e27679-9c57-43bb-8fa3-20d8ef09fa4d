<div class="flex justify-center items-center min-h-[calc(100vh-64px)] bg-surface p-6 box-border">
  <div class="bg-background rounded-md shadow-md p-8 w-full max-w-lg text-center border-l-4 border-primary">
    <h2 class="text-2xl font-bold text-primary mb-6">Email Verification</h2>

    <div *ngIf="isLoading" class="flex flex-col items-center my-6">
      <p class="text-text-secondary">{{ message }}</p>
      <div class="border-4 border-gray-200 border-t-primary rounded-full w-10 h-10 animate-spin mt-4"></div>
    </div>

    <div *ngIf="!isLoading" class="mt-5">
      <div *ngIf="isSuccess" class="text-success font-medium py-4">
        <p>{{ message }}</p>
        <p class="text-sm text-text-secondary mt-2 italic">You will be redirected to the login page in a few seconds...</p>
        <div class="flex justify-center gap-4 mt-6 flex-wrap">
          <button (click)="navigateToLogin()" 
                  class="px-5 py-2 bg-primary text-on-primary rounded-sm text-base shadow-sm hover:bg-primary-medium transition-colors">Go to Login</button>
          <button (click)="navigateToDashboard()" 
                  class="px-5 py-2 bg-surface text-text-primary border border-border rounded-sm text-base shadow-sm hover:bg-border transition-colors">Go to Dashboard</button>
        </div>
      </div>

      <div *ngIf="!isSuccess" class="text-error font-medium text-left py-4">
        <p>{{ message }}</p>
        <p class="mt-4 text-sm text-text-secondary font-normal">If you're having trouble with verification:</p>
        <ul class="list-disc pl-10 mt-2 text-sm text-text-secondary font-normal">
          <li class="mb-1">Check your email inbox and spam folder</li>
          <li class="mb-1">Try clicking the verification link directly from your email</li>
          <li class="mb-1">The verification link may have expired (valid for 24 hours)</li>
        </ul>
        <div class="flex justify-center gap-4 mt-6 flex-wrap">
          <button (click)="resendVerificationEmail()" 
                  class="px-5 py-2 bg-primary text-on-primary rounded-sm text-base shadow-sm hover:bg-primary-medium transition-colors">Request New Verification Email</button>
          <button (click)="navigateToLogin()" 
                  class="px-5 py-2 bg-surface text-text-primary border border-border rounded-sm text-base shadow-sm hover:bg-border transition-colors">Return to Login</button>
        </div>
      </div>
    </div>
  </div>
</div>
