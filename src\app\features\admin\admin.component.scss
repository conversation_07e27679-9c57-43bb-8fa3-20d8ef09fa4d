/* src/app/features/admin/admin.component.scss */

// Main container for the admin page
.admin-container {
  padding: var(--spacing-lg); // 24px
  margin: 0 auto;
  max-width: 1200px; // Consistent max width
}

// Page Title
.admin-title {
  font-size: var(--font-size-h2); // Use defined H2 size
  font-weight: var(--font-weight-bold);
  color: var(--text-primary-color);
  margin-bottom: var(--spacing-lg); // 24px
}

// Loading spinner
.loading-spinner-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xxl); // 48px
  
  p {
    margin-top: var(--spacing-md);
    font-size: var(--font-size-lg);
    color: var(--text-secondary-color);
  }
}

// Message Cards (Error, Success)
.message-card {
  margin-bottom: var(--spacing-lg); // 24px
  padding: var(--spacing-md); // 16px

  .message-content {
    display: flex;
    align-items: center;
    
    mat-icon {
      margin-right: var(--spacing-sm); // 8px
      font-size: var(--font-size-xl); // 20px
    }
    span {
      font-size: var(--font-size-md);
    }
  }

  &.error-card {
    background-color: rgba(var(--error-color-rgb), 0.1);
    color: var(--error-color);
    border-left: 4px solid var(--error-color);
    mat-icon { color: var(--error-color); }
  }

  &.success-card {
    background-color: rgba(var(--success-color-rgb), 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
    mat-icon { color: var(--success-color); }
  }
}

// Angular Material Tab Group styling
.admin-tab-group {
  margin-bottom: var(--spacing-lg); // 24px
  // Mat-tab-group has its own theming, but we can adjust if needed
  // Example: Changing the ink bar color
  // ::ng-deep .mat-mdc-tab-header .mat-mdc-tab-ink-bar {
  //   background-color: var(--accent-color) !important;
  // }
}

// Wrapper for content within each tab
.tab-content-wrapper {
  padding: var(--spacing-lg) 0; // Add some padding above/below tab content
}

// Section titles within tabs
.tab-section-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary-color);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

// Form Card styling
.form-card {
  margin-bottom: var(--spacing-xl); // 32px
}

// Generic styles for forms using Material components
.admin-form-material {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md); // Spacing between form fields

  .form-field-full-width {
    width: 100%;
  }
  
  .form-error-message {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: rgba(var(--error-color-rgb), 0.05);
    border-radius: var(--border-radius-sm);
    margin-top: var(--spacing-xs);
  }

  .form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
    justify-content: flex-start; // Align buttons to the start
  }
}

// Informational boxes (like the one in Payday Dates tab)
.info-box {
  padding: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  background-color: rgba(var(--primary-color-rgb), 0.07); // Subtle primary background
  color: var(--text-secondary-color); // Use secondary text color for info
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);

  .info-icon {
    margin-right: var(--spacing-sm);
    color: var(--primary-color); // Icon color matching border
  }

  &.warning-box { // For the App Modules warning
    background-color: rgba(var(--accent-color-rgb), 0.08);
    color: var(--text-secondary-color);
    border-color: rgba(var(--accent-color-rgb), 0.3);
    .info-icon { color: var(--accent-color); }
  }
}


// Table Container Styling
.table-container {
  margin-bottom: var(--spacing-xl); // 32px
  border-radius: var(--border-radius-md);
  overflow: hidden; // Ensures border-radius clips table corners
}

.admin-mat-table {
  width: 100%;

  // Sticky header for tables
  .mat-mdc-header-row {
    position: sticky;
    top: 0;
    z-index: 10; // Ensure header is above scrolling content
    background-color: var(--primary-color); // Ensure header background is solid

    .mat-mdc-header-cell {
      color: var(--on-primary-color);
      font-weight: var(--font-weight-medium);
    }
  }
  
  .mat-mdc-cell, .mat-mdc-header-cell {
    padding: var(--spacing-sm) var(--spacing-md); // Consistent padding
  }

  .mat-mdc-row:hover {
    background-color: rgba(0,0,0, 0.03); // Subtle hover
  }
  
  .mat-mdc-no-data-row .mat-mdc-cell {
    text-align: center;
    font-style: italic;
    color: var(--text-secondary-color);
    padding: var(--spacing-lg);
  }

  // Specific styling for action buttons in tables
  .mat-mdc-cell .mat-mdc-button-base, // Targets mat-icon-button, mat-button, etc.
  .mat-mdc-cell .mat-mdc-slide-toggle { 
    margin-right: var(--spacing-xs);
    &:last-child {
      margin-right: 0;
    }
  }
  .actions-column-header { // Center align actions header
    text-align: center !important; // Override default left align from mat-header-cell
  }
  .actions-cell { // Center align action buttons
    text-align: center !important;
  }
}

// Status Badges for Modules tab
.status-badge {
  padding: var(--spacing-xxs) var(--spacing-sm); // 2px 8px
  border-radius: var(--border-radius-lg); // Make it pill-shaped
  font-size: var(--font-size-xs); // 12px
  font-weight: var(--font-weight-medium);
  display: inline-block; // Ensure padding and radius apply correctly

  &.status-enabled, &.status-basic {
    background-color: rgba(var(--success-color-rgb), 0.15);
    color: var(--success-color);
  }
  &.status-disabled {
    background-color: rgba(var(--disabled-color-rgb, 189, 189, 189), 0.2); // Define --disabled-color-rgb or use direct
    color: var(--disabled-color);
  }
  &.status-premium {
    background-color: rgba(var(--accent-color-rgb), 0.15);
    color: var(--accent-color);
  }
}


// Statistics Tab Styling
.stats-tab-wrapper {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg); // 24px
  }

  .stat-card {
    .mat-mdc-card-header .mat-mdc-card-avatar { // Adjust avatar icon size and color
      font-size: var(--font-size-h2); // 32px
      color: var(--primary-color);
      margin-right: var(--spacing-md);
    }
    .mat-mdc-card-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
    }
    .stat-value {
      font-size: var(--font-size-h1); // 40px
      font-weight: var(--font-weight-bold);
      color: var(--primary-color);
      text-align: center;
      margin: var(--spacing-md) 0;
    }
    &.full-width-stat-card { // Allow a card to span full width if needed
      grid-column: 1 / -1;
    }
  }
}


/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .admin-title {
    font-size: var(--font-size-h3); // Slightly smaller title
  }

  .admin-form-material {
    .form-actions {
      flex-direction: column; // Stack buttons on small screens
      button {
        width: 100%;
        margin-left: 0 !important; // Remove left margin if stacked
      }
    }
  }
  
  .stats-tab-wrapper .stats-grid {
    grid-template-columns: 1fr; // Stack stat cards
  }

  // Make table action buttons stack vertically if needed, or ensure enough space
  .admin-mat-table .actions-cell {
    // Consider stacking buttons or ensuring they wrap if too many
    // display: flex;
    // flex-direction: column;
    // gap: var(--spacing-xs);
    // align-items: flex-start;
  }
}
