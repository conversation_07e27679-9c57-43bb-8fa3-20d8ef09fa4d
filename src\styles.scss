/* styles.scss */
// Import Angular Material theming
@use '@angular/material' as mat;
// Import predefined palettes
@use '@angular/material/prebuilt-themes/indigo-pink.css';

// Rule 3.a & 3.b.iii: Replace @import with @use and namespace for _variables.scss
// Using the correct full filename '_variables.scss' for the path.
@use './assets/styles/_variables' as projectVariables;

// Rule 3.b.iv: @include mat.core();
// Rule 3.d: Theme definitions come *after* mat.core()
@include mat.core();

// Define palettes from our colors.
// As established, _variables.scss contains only CSS custom properties (e.g. --primary-color),
// not SASS variables (e.g. $primary-color).
// The mat.define-palette calls below use hardcoded hex values (e.g. #1976D2)
// or Material's own SASS variables (e.g. mat.$dark-primary-text).
// Therefore, no changes are needed here for projectVariables.something, as per Task 3.c.

/*
$custom-primary-palette: mat.define-palette((
  50: #BBDEFB,
  100: #90CAF9,
  200: #64B5F6,
  300: #42A5F5,
  400: #2196F3,
  500: #1976D2,
  600: #1565C0,
  700: #1565C0,
  800: #0D47A1,
  900: #0D47A1,
  A100: #82B1FF,
  A200: #448AFF,
  A400: #2979FF,
  A700: #2962FF,
  contrast: (
    50: #000000,
    100: #000000,
    200: #000000,
    300: #ffffff,
    400: #ffffff,
    500: #ffffff,
    600: #ffffff,
    700: #ffffff,
    800: #ffffff,
    900: #ffffff,
    A100: #000000,
    A200: #ffffff,
    A400: #ffffff,
    A700: #ffffff,
  )
));

$custom-accent-palette: mat.define-palette((
  50: #FFECB3,
  100: #FFE082,
  200: #FFD54F,
  300: #FFCA28,
  400: #FFC107,
  500: #FFB300,
  600: #FFA000,
  700: #FF8F00,
  800: #FF6F00,
  900: #FF6F00,
  A100: #FFE57F,
  A200: #FFD740,
  A400: #FFC400,
  A700: #FFAB00,
  contrast: (
    50: #000000,
    100: #000000,
    200: #000000,
    300: #000000,
    400: #000000,
    500: #000000,
    600: #000000,
    700: #000000,
    800: #000000,
    900: #000000,
    A100: #000000,
    A200: #000000,
    A400: #000000,
    A700: #000000,
  )
));

$custom-warn-palette: mat.define-palette(mat.$red-palette, 700);

$custom-theme: mat.define-light-theme((
  color: (
    primary: $custom-primary-palette,
    accent: $custom-accent-palette,
    warn: $custom-warn-palette,
  ),
  typography: mat.define-typography-config(),
  density: 0,
));

@include mat.all-component-themes($custom-theme);
*/

// Using the prebuilt theme instead of custom theme configuration
// This simplifies the setup and avoids SASS compilation issues

// Global styles
// These styles use var(--css-custom-property-name) which are defined in _variables.scss.
// This usage is standard CSS and does not require SASS namespacing.
html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: var(--font-family-base);
  font-size: var(--font-size-md);
  line-height: var(--line-height-base);
  color: var(--text-primary-color);
  background-color: var(--surface-color);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-base);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary-color);
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-h1); }
h2 { font-size: var(--font-size-h2); }
h3 { font-size: var(--font-size-h3); }
h4 { font-size: var(--font-size-h4); }
h5 { font-size: var(--font-size-h5); }
h6 { font-size: var(--font-size-h6); }

p {
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-md);
  line-height: var(--line-height-base);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

a:hover {
  color: var(--accent-color);
  text-decoration: underline;
}

/* Utility Classes */
.text-left   { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right  { text-align: right !important; }

/* Margins */
.m-0 { margin: 0 !important; }
.mt-0 { margin-top: 0 !important; }
.mr-0 { margin-right: 0 !important; }
.mb-0 { margin-bottom: 0 !important; }
.ml-0 { margin-left: 0 !important; }
.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.m-xxs { margin: var(--spacing-xxs) !important; }
.mt-xxs { margin-top: var(--spacing-xxs) !important; }
.mr-xxs { margin-right: var(--spacing-xxs) !important; }
.mb-xxs { margin-bottom: var(--spacing-xxs) !important; }
.ml-xxs { margin-left: var(--spacing-xxs) !important; }
.mx-xxs { margin-left: var(--spacing-xxs) !important; margin-right: var(--spacing-xxs) !important; }
.my-xxs { margin-top: var(--spacing-xxs) !important; margin-bottom: var(--spacing-xxs) !important; }
.m-xs { margin: var(--spacing-xs) !important; }
.mt-xs { margin-top: var(--spacing-xs) !important; }
.mr-xs { margin-right: var(--spacing-xs) !important; }
.mb-xs { margin-bottom: var(--spacing-xs) !important; }
.ml-xs { margin-left: var(--spacing-xs) !important; }
.mx-xs { margin-left: var(--spacing-xs) !important; margin-right: var(--spacing-xs) !important; }
.my-xs { margin-top: var(--spacing-xs) !important; margin-bottom: var(--spacing-xs) !important; }
.m-sm { margin: var(--spacing-sm) !important; }
.mt-sm { margin-top: var(--spacing-sm) !important; }
.mr-sm { margin-right: var(--spacing-sm) !important; }
.mb-sm { margin-bottom: var(--spacing-sm) !important; }
.ml-sm { margin-left: var(--spacing-sm) !important; }
.mx-sm { margin-left: var(--spacing-sm) !important; margin-right: var(--spacing-sm) !important; }
.my-sm { margin-top: var(--spacing-sm) !important; margin-bottom: var(--spacing-sm) !important; }
.m-md { margin: var(--spacing-md) !important; }
.mt-md { margin-top: var(--spacing-md) !important; }
.mr-md { margin-right: var(--spacing-md) !important; }
.mb-md { margin-bottom: var(--spacing-md) !important; }
.ml-md { margin-left: var(--spacing-md) !important; }
.mx-md { margin-left: var(--spacing-md) !important; margin-right: var(--spacing-md) !important; }
.my-md { margin-top: var(--spacing-md) !important; margin-bottom: var(--spacing-md) !important; }
.m-lg { margin: var(--spacing-lg) !important; }
.mt-lg { margin-top: var(--spacing-lg) !important; }
.mr-lg { margin-right: var(--spacing-lg) !important; }
.mb-lg { margin-bottom: var(--spacing-lg) !important; }
.ml-lg { margin-left: var(--spacing-lg) !important; }
.mx-lg { margin-left: var(--spacing-lg) !important; margin-right: var(--spacing-lg) !important; }
.my-lg { margin-top: var(--spacing-lg) !important; margin-bottom: var(--spacing-lg) !important; }
.m-xl { margin: var(--spacing-xl) !important; }
.mt-xl { margin-top: var(--spacing-xl) !important; }
.mr-xl { margin-right: var(--spacing-xl) !important; }
.mb-xl { margin-bottom: var(--spacing-xl) !important; }
.ml-xl { margin-left: var(--spacing-xl) !important; }
.mx-xl { margin-left: var(--spacing-xl) !important; margin-right: var(--spacing-xl) !important; }
.my-xl { margin-top: var(--spacing-xl) !important; margin-bottom: var(--spacing-xl) !important; }
.m-xxl { margin: var(--spacing-xxl) !important; }
.mt-xxl { margin-top: var(--spacing-xxl) !important; }
.mr-xxl { margin-right: var(--spacing-xxl) !important; }
.mb-xxl { margin-bottom: var(--spacing-xxl) !important; }
.ml-xxl { margin-left: var(--spacing-xxl) !important; }
.mx-xxl { margin-left: var(--spacing-xxl) !important; margin-right: var(--spacing-xxl) !important; }
.my-xxl { margin-top: var(--spacing-xxl) !important; margin-bottom: var(--spacing-xxl) !important; }
.m-xxxl { margin: var(--spacing-xxxl) !important; }
.mt-xxxl { margin-top: var(--spacing-xxxl) !important; }
.mr-xxxl { margin-right: var(--spacing-xxxl) !important; }
.mb-xxxl { margin-bottom: var(--spacing-xxxl) !important; }
.ml-xxxl { margin-left: var(--spacing-xxxl) !important; }
.mx-xxxl { margin-left: var(--spacing-xxxl) !important; margin-right: var(--spacing-xxxl) !important; }
.my-xxxl { margin-top: var(--spacing-xxxl) !important; margin-bottom: var(--spacing-xxxl) !important; }

/* Paddings */
.p-0 { padding: 0 !important; }
.pt-0 { padding-top: 0 !important; }
.pr-0 { padding-right: 0 !important; }
.pb-0 { padding-bottom: 0 !important; }
.pl-0 { padding-left: 0 !important; }
.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.p-xxs { padding: var(--spacing-xxs) !important; }
.pt-xxs { padding-top: var(--spacing-xxs) !important; }
.pr-xxs { padding-right: var(--spacing-xxs) !important; }
.pb-xxs { padding-bottom: var(--spacing-xxs) !important; }
.pl-xxs { padding-left: var(--spacing-xxs) !important; }
.px-xxs { padding-left: var(--spacing-xxs) !important; padding-right: var(--spacing-xxs) !important; }
.py-xxs { padding-top: var(--spacing-xxs) !important; padding-bottom: var(--spacing-xxs) !important; }
.p-xs { padding: var(--spacing-xs) !important; }
.pt-xs { padding-top: var(--spacing-xs) !important; }
.pr-xs { padding-right: var(--spacing-xs) !important; }
.pb-xs { padding-bottom: var(--spacing-xs) !important; }
.pl-xs { padding-left: var(--spacing-xs) !important; }
.px-xs { padding-left: var(--spacing-xs) !important; padding-right: var(--spacing-xs) !important; }
.py-xs { padding-top: var(--spacing-xs) !important; padding-bottom: var(--spacing-xs) !important; }
.p-sm { padding: var(--spacing-sm) !important; }
.pt-sm { padding-top: var(--spacing-sm) !important; }
.pr-sm { padding-right: var(--spacing-sm) !important; }
.pb-sm { padding-bottom: var(--spacing-sm) !important; }
.pl-sm { padding-left: var(--spacing-sm) !important; }
.px-sm { padding-left: var(--spacing-sm) !important; padding-right: var(--spacing-sm) !important; }
.py-sm { padding-top: var(--spacing-sm) !important; padding-bottom: var(--spacing-sm) !important; }
.p-md { padding: var(--spacing-md) !important; }
.pt-md { padding-top: var(--spacing-md) !important; }
.pr-md { padding-right: var(--spacing-md) !important; }
.pb-md { padding-bottom: var(--spacing-md) !important; }
.pl-md { padding-left: var(--spacing-md) !important; }
.px-md { padding-left: var(--spacing-md) !important; padding-right: var(--spacing-md) !important; }
.py-md { padding-top: var(--spacing-md) !important; padding-bottom: var(--spacing-md) !important; }
.p-lg { padding: var(--spacing-lg) !important; }
.pt-lg { padding-top: var(--spacing-lg) !important; }
.pr-lg { padding-right: var(--spacing-lg) !important; }
.pb-lg { padding-bottom: var(--spacing-lg) !important; }
.pl-lg { padding-left: var(--spacing-lg) !important; }
.px-lg { padding-left: var(--spacing-lg) !important; padding-right: var(--spacing-lg) !important; }
.py-lg { padding-top: var(--spacing-lg) !important; padding-bottom: var(--spacing-lg) !important; }
.p-xl { padding: var(--spacing-xl) !important; }
.pt-xl { padding-top: var(--spacing-xl) !important; }
.pr-xl { padding-right: var(--spacing-xl) !important; }
.pb-xl { padding-bottom: var(--spacing-xl) !important; }
.pl-xl { padding-left: var(--spacing-xl) !important; }
.px-xl { padding-left: var(--spacing-xl) !important; padding-right: var(--spacing-xl) !important; }
.py-xl { padding-top: var(--spacing-xl) !important; padding-bottom: var(--spacing-xl) !important; }
.p-xxl { padding: var(--spacing-xxl) !important; }
.pt-xxl { padding-top: var(--spacing-xxl) !important; }
.pr-xxl { padding-right: var(--spacing-xxl) !important; }
.pb-xxl { padding-bottom: var(--spacing-xxl) !important; }
.pl-xxl { padding-left: var(--spacing-xxl) !important; }
.px-xxl { padding-left: var(--spacing-xxl) !important; padding-right: var(--spacing-xxl) !important; }
.py-xxl { padding-top: var(--spacing-xxl) !important; padding-bottom: var(--spacing-xxl) !important; }
.p-xxxl { padding: var(--spacing-xxxl) !important; }
.pt-xxxl { padding-top: var(--spacing-xxxl) !important; }
.pr-xxxl { padding-right: var(--spacing-xxxl) !important; }
.pb-xxxl { padding-bottom: var(--spacing-xxxl) !important; }
.pl-xxxl { padding-left: var(--spacing-xxxl) !important; }
.px-xxxl { padding-left: var(--spacing-xxxl) !important; padding-right: var(--spacing-xxxl) !important; }
.py-xxxl { padding-top: var(--spacing-xxxl) !important; padding-bottom: var(--spacing-xxxl) !important; }

/* Flexbox Utilities */
.d-flex { display: flex !important; }
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.justify-content-start   { justify-content: flex-start !important; }
.justify-content-end     { justify-content: flex-end !important; }
.justify-content-center  { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around  { justify-content: space-around !important; }
.justify-content-evenly  { justify-content: space-evenly !important; }
.align-items-start    { align-items: flex-start !important; }
.align-items-end      { align-items: flex-end !important; }
.align-items-center   { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch  { align-items: stretch !important; }
.align-self-start    { align-self: flex-start !important; }
.align-self-end      { align-self: flex-end !important; }
.align-self-center   { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch  { align-self: stretch !important; }
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

/* Visibility */
.hidden { display: none !important; }

/* Global Material Component Enhancements */
.mat-mdc-card {
  transition: box-shadow 0.3s ease-in-out, transform 0.2s ease-in-out !important;
}

/* Animation for content fade-in */
@keyframes fadeInContent {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.content-fade-in {
  animation: fadeInContent 0.5s ease-in-out forwards;
}
