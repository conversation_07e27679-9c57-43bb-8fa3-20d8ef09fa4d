// src/app/shared/shared.module.ts
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { FeatureFlagDirective } from './directives/feature-flag.directive';

/**
 * Shared module that provides common functionality across the application
 * All components are standalone, so this module only re-exports common modules
 */
@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    FeatureFlagDirective
  ],
  exports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    FeatureFlagDirective
  ]
})
export class SharedModule { }
