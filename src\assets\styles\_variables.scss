:root {
  // New Modern Color Palette
  --primary-color: #1976D2;            // Material Design Blue 700
  --accent-color: #FFB300;             // Material Design Amber 700
  --surface-color: #F5F7FA;           // Light Gray - Page background
  --background-color: #FFFFFF;         // White - Card/component backgrounds
  --on-primary-color: #FFFFFF;
  --on-accent-color: #212121;           // Dark Gray for text on accent
  --text-primary-color: #212121;       // Dark Gray
  --text-secondary-color: #757575;     // Medium Gray
  --error-color: #D32F2F;              // Material Design Red 700
  --success-color: #388E3C;            // Material Design Green 700
  --success-color-rgb: 56, 142, 60;
  --border-color: #E0E0E0;             // Light Gray for borders
  --disabled-color: #BDBDBD;           // Material Design Grey 400 (for text/icons)
  --disabled-background-color: #E0E0E0; // Material Design Grey 300 (for component backgrounds)

  // RGB versions for use in rgba()
  --primary-color-rgb: 25, 118, 210;
  --accent-color-rgb: 255, 179, 0;
  --error-color-rgb: 211, 47, 47;
  --on-primary-color-rgb: 255, 255, 255;
  // --text-primary-color-rgb: 33, 33, 33; // If needed for rgba()

  // Glow effect colors (RGB for use in rgba())
  --holiday-glow-color-rgb: 30, 144, 255; // Dodger Blue for holiday events
  --payday-glow-color-rgb: 255, 215, 0;   // Gold for payday events

  // It's good practice to define a lighter/darker shade for primary if used often, e.g., for hovers
  --primary-color-light: #64b5f6; // Material Design Blue 300 - A lighter shade of primary
  --primary-color-dark: #1565C0;  // Material Design Blue 800 - A darker shade of primary


  // Typography
  --font-family-base: 'Roboto', 'Helvetica Neue', sans-serif;
  --font-size-base: 16px; // Base font size for rem calculations
  --line-height-base: 1.5;

  --font-size-xs: 0.75rem;   // 12px
  --font-size-sm: 0.875rem;  // 14px
  --font-size-md: 1rem;      // 16px (base)
  --font-size-lg: 1.125rem;  // 18px
  --font-size-xl: 1.25rem;   // 20px
  --font-size-xxl: 1.5rem;   // 24px

  --font-size-h1: 2.5rem;    // 40px
  --font-size-h2: 2rem;      // 32px
  --font-size-h3: 1.75rem;   // 28px
  --font-size-h4: 1.5rem;    // 24px (same as xxl)
  --font-size-h5: 1.25rem;   // 20px (same as xl)
  --font-size-h6: 1rem;      // 16px (same as md)

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  // Spacing Units
  --spacing-unit: 8px;
  --spacing-xxs: calc(var(--spacing-unit) * 0.25); // 2px
  --spacing-xs:  calc(var(--spacing-unit) * 0.5);  // 4px
  --spacing-sm:  calc(var(--spacing-unit) * 1);    // 8px
  --spacing-md:  calc(var(--spacing-unit) * 2);    // 16px
  --spacing-lg:  calc(var(--spacing-unit) * 3);    // 24px
  --spacing-xl:  calc(var(--spacing-unit) * 4);    // 32px
  --spacing-xxl: calc(var(--spacing-unit) * 6);    // 48px
  --spacing-xxxl: calc(var(--spacing-unit) * 8);   // 64px

  // Border Radius
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;

  // Shadows (Material Design Inspired)
  // Based on https://material.io/design/environment/elevation.html#depicting-elevation-in-material
  // Values are simplified for common use cases.
  --shadow-umbra-opacity: 0.2;
  --shadow-penumbra-opacity: 0.14;
  --shadow-ambient-opacity: 0.12;

  --shadow-dp0: none;
  --shadow-dp1: 0px 2px 1px -1px rgba(0,0,0,var(--shadow-umbra-opacity)),
                0px 1px 1px 0px rgba(0,0,0,var(--shadow-penumbra-opacity)),
                0px 1px 3px 0px rgba(0,0,0,var(--shadow-ambient-opacity));
  --shadow-dp2: 0px 3px 1px -2px rgba(0,0,0,var(--shadow-umbra-opacity)),
                0px 2px 2px 0px rgba(0,0,0,var(--shadow-penumbra-opacity)),
                0px 1px 5px 0px rgba(0,0,0,var(--shadow-ambient-opacity));
  --shadow-dp3: 0px 3px 3px -2px rgba(0,0,0,var(--shadow-umbra-opacity)),
                0px 3px 4px 0px rgba(0,0,0,var(--shadow-penumbra-opacity)),
                0px 1px 8px 0px rgba(0,0,0,var(--shadow-ambient-opacity));
  --shadow-dp4: 0px 2px 4px -1px rgba(0,0,0,var(--shadow-umbra-opacity)),
                0px 4px 5px 0px rgba(0,0,0,var(--shadow-penumbra-opacity)),
                0px 1px 10px 0px rgba(0,0,0,var(--shadow-ambient-opacity));
  --shadow-dp6: 0px 3px 5px -1px rgba(0,0,0,var(--shadow-umbra-opacity)),
                0px 6px 10px 0px rgba(0,0,0,var(--shadow-penumbra-opacity)),
                0px 1px 18px 0px rgba(0,0,0,var(--shadow-ambient-opacity));
  --shadow-dp8: 0px 5px 5px -3px rgba(0,0,0,var(--shadow-umbra-opacity)),
                0px 8px 10px 1px rgba(0,0,0,var(--shadow-penumbra-opacity)),
                0px 3px 14px 2px rgba(0,0,0,var(--shadow-ambient-opacity));
  --shadow-dp12: 0px 7px 8px -4px rgba(0,0,0,var(--shadow-umbra-opacity)),
                 0px 12px 17px 2px rgba(0,0,0,var(--shadow-penumbra-opacity)),
                 0px 5px 22px 4px rgba(0,0,0,var(--shadow-ambient-opacity));
  --shadow-dp16: 0px 8px 10px -5px rgba(0,0,0,var(--shadow-umbra-opacity)),
                 0px 16px 24px 2px rgba(0,0,0,var(--shadow-penumbra-opacity)),
                 0px 6px 30px 5px rgba(0,0,0,var(--shadow-ambient-opacity));
  --shadow-dp24: 0px 11px 15px -7px rgba(0,0,0,var(--shadow-umbra-opacity)),
                 0px 24px 38px 3px rgba(0,0,0,var(--shadow-penumbra-opacity)),
                 0px 9px 46px 8px rgba(0,0,0,var(--shadow-ambient-opacity));
}
