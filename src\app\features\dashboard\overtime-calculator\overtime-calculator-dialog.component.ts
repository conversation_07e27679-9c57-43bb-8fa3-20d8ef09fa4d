import { Component, OnInit, Inject, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SupabaseService } from '../../../core/services/supabase.service';

@Component({
  selector: 'app-overtime-calculator-dialog',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatCardModule,
    MatDividerModule,
    MatTooltipModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './overtime-calculator-dialog.component.html',
  styleUrls: ['./overtime-calculator-dialog.component.scss']
})
export class OvertimeCalculatorDialogComponent implements OnInit {
  calculatorForm: FormGroup;

  // Calculation results
  baseHourlyRate: number = 0;
  overtimePayRate: number = 0;
  totalGrossOvertimePay: number = 0;

  // Constants
  readonly ANNUAL_REGULAR_HOURS: number = 2132; // 41 hours/week * 52 weeks
  readonly OVERTIME_MULTIPLIER: number = 1.5;

  constructor(
    public dialogRef: MatDialogRef<OvertimeCalculatorDialogComponent>,
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.calculatorForm = this.fb.group({
      monthlyBaseSalary: [8936, [Validators.required, Validators.min(0.01)]],
      totalOvertimeHours: [0, [Validators.required, Validators.min(0)]]
    });
  }

  async ngOnInit(): Promise<void> {
    // Get current OT hours from the dashboard
    await this.getCurrentOtHours();

    // Calculate initial values
    this.calculateOvertimePay();
  }

  /**
   * Gets the current OT hours from the current OT period to auto-populate the form
   */
  async getCurrentOtHours(): Promise<void> {
    try {
      // Get current user from the observable
      const currentUser = await new Promise<any>((resolve) => {
        const subscription = this.supabaseService.currentUser$.subscribe(user => {
          if (user && typeof user !== 'boolean') {
            resolve(user);
          } else {
            resolve(null);
          }
          subscription.unsubscribe();
        });
      });

      if (!currentUser) return;

      // Find the current OT period (the one that includes today's date)
      const otPeriods = await this.supabaseService.getAllOtPeriods();
      if (otPeriods.length === 0) return;

      const today = new Date().toISOString().split('T')[0];
      const currentPeriodIndex = otPeriods.findIndex(period =>
        period.start_date <= today && period.end_date >= today
      );

      // If no current period found, return
      if (currentPeriodIndex < 0) return;

      const currentOtPeriod = otPeriods[currentPeriodIndex];

      // Get time entries for the current OT period
      const timeEntries = await this.supabaseService.getTimeEntriesForUser(
        currentUser.id,
        currentOtPeriod.start_date,
        currentOtPeriod.end_date
      );

      // Calculate OT hours
      let otHours = 0;

      timeEntries.forEach(entry => {
        if (entry.entry_type === 'OT_VOLUNTARY' || entry.entry_type === 'OT_MANDATED') {
          const startTime = new Date(entry.start_time);
          const endTime = new Date(entry.end_time);

          // Calculate duration in hours
          const durationMs = endTime.getTime() - startTime.getTime();
          const durationHours = durationMs / (1000 * 60 * 60);

          otHours += durationHours;
        }
      });

      // Update the form with the calculated OT hours
      this.calculatorForm.patchValue({
        totalOvertimeHours: parseFloat(otHours.toFixed(2))
      });
    } catch (error) {
      console.error('Error getting current OT hours:', error);
      // If there's an error, we'll just use the default value
    }
  }

  calculateOvertimePay(): void {
    if (this.calculatorForm.valid) {
      const monthlyBaseSalary = this.calculatorForm.get('monthlyBaseSalary')?.value || 0;
      const totalOvertimeHours = this.calculatorForm.get('totalOvertimeHours')?.value || 0;

      // Calculate base hourly rate
      this.baseHourlyRate = (monthlyBaseSalary * 12) / this.ANNUAL_REGULAR_HOURS;

      // Regular Rate of Pay (RROP) - simplified to equal base hourly rate
      const rrop = this.baseHourlyRate;

      // Calculate overtime pay rate
      this.overtimePayRate = rrop * this.OVERTIME_MULTIPLIER;

      // Calculate total gross overtime pay
      this.totalGrossOvertimePay = totalOvertimeHours * this.overtimePayRate;
    }
  }
}
