import { Component, OnInit, OnD<PERSON>roy, HostListener } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { TabBarComponent } from './layout/tab-bar/tab-bar.component';
import { SidebarComponent } from './layout/sidebar/sidebar.component';
import { SearchBarComponent } from './shared/components/search-bar/search-bar.component';
import { MatSidenavModule, MatDrawerMode } from '@angular/material/sidenav';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { SupabaseService } from './core/services/supabase.service';
import { Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    TabBarComponent,
    SidebarComponent,
    SearchBarComponent,
    MatSidenavModule,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'Electronic Time Book'; // Updated title for display
  isAuthenticated = false;
  isMobile = false;
  sidenavMode: MatDrawerMode = 'side'; // Default mode
  sidenavOpened = true; // Default state
  private authSubscription: Subscription | null = null;
  private readonly MOBILE_BREAKPOINT = 768; // Match with CSS media queries

  constructor(private supabaseService: SupabaseService) {
    // Initialize screen size detection
    this.checkScreenSize();
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.checkScreenSize();
  }

  private checkScreenSize() {
    this.isMobile = window.innerWidth <= this.MOBILE_BREAKPOINT;
    this.sidenavMode = this.isMobile ? 'over' : 'side';
    this.sidenavOpened = !this.isMobile;
  }

  ngOnInit(): void {
    // Subscribe to authentication state changes
    this.authSubscription = this.supabaseService.currentUser$.subscribe(user => {
      this.isAuthenticated = !!user && typeof user !== 'boolean';
    });
  }

  ngOnDestroy(): void {
    // Clean up subscription
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }
}
