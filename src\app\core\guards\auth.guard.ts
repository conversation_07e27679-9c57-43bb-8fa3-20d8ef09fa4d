// src/app/core/guards/auth.guard.ts
import { inject } from '@angular/core';
import { CanActivateFn, Router, UrlTree } from '@angular/router';
import { SupabaseService } from '../services/supabase.service'; // Adjust path if needed
import { Observable, map, take } from 'rxjs';

export const authGuard: CanActivateFn = (route, state):
  | Observable<boolean | UrlTree>
  | Promise<boolean | UrlTree>
  | boolean
  | UrlTree => {

  const supabaseService = inject(SupabaseService);
  const router = inject(Router);

  // First check if we have a session in localStorage as a quick check
  const storedSession = localStorage.getItem('etb_user_session');
  if (storedSession) {
    try {
      const session = JSON.parse(storedSession);
      // Basic validation of the session structure
      if (session && session.access_token && session.user) {
        // We have a stored session, but we'll still verify with Supabase
        // This allows immediate access while the verification happens in the background

        // Verify the session with Supa<PERSON> in the background
        supabaseService.auth.getSession().then(({ data }) => {
          if (!data.session) {
            // Session is invalid, clear it and redirect to login
            localStorage.removeItem('etb_user_session');
            router.navigate(['/auth/login']);
          }
        }).catch(() => {
          // Error verifying session, clear it and redirect to login
          localStorage.removeItem('etb_user_session');
          router.navigate(['/auth/login']);
        });

        return true;
      }
    } catch (e) {
      // Invalid stored session, continue with normal flow
      localStorage.removeItem('etb_user_session');
    }
  }

  // Use take(1) to get the current value and complete
  return supabaseService.currentSession$.pipe(
    take(1), // Important: take the first emission only
    map(session => {
      const isLoggedIn = !!session; // Check if session is not null

      if (isLoggedIn) {
        return true; // Allow access to the route
      } else {
        // Not logged in, redirect to login page
        return router.createUrlTree(['/auth/login']); // Create UrlTree for redirection
      }
    })
  );
};