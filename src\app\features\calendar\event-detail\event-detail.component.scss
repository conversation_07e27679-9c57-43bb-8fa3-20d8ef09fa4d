.full-width {
  width: 100%;
  margin-bottom: var(--spacing-md); // Use theme variable

  // Fix for label spacing (Review if still needed with Material 15+)
  // These might be overly specific or could conflict with default Material styling.
  // It's often better to rely on mat-form-field appearance and standard Material behaviors.
  /*
  ::ng-deep .mat-form-field-infix {
    padding-top: 1em;
  }

  ::ng-deep .mat-form-field-label-wrapper {
    top: -0.5em;
    padding-top: 0.75em;
  }

  // Ensure the label has enough space and is fully visible
  ::ng-deep .mat-form-field-label {
    top: 1.7em;
    margin-top: 0.25em;
  }
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-lg); // Updated
  font-style: italic;
  color: var(--text-secondary-color); // Updated
}

.error-message {
  background-color: rgba(var(--error-color-rgb), 0.1); // Updated
  color: var(--error-color); // Updated
  padding: var(--spacing-sm); // Updated
  border-radius: var(--border-radius-sm); // Updated
  margin-bottom: var(--spacing-md); // Updated
}

  */
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-lg); 
  font-style: italic;
  color: var(--text-secondary-color); 
}

.error-message {
  background-color: rgba(var(--error-color-rgb), 0.1); 
  color: var(--error-color); 
  padding: var(--spacing-sm); 
  border-radius: var(--border-radius-sm); 
  margin-bottom: var(--spacing-md); 
}

mat-dialog-content {
  min-width: 400px; // Keep as is or make responsive if needed
  padding: var(--spacing-lg); 
  padding-top: var(--spacing-md); // Use theme variable (15px is close to 16px)

  // This ::ng-deep might be too aggressive or outdated with newer Material versions.
  // Spacing should ideally be handled by mat-form-field itself or theme.
  /*
  ::ng-deep .mat-form-field-wrapper {
    padding-bottom: 1.25em; 
  }
  */
}

.holiday-form,
.time-entry-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md); // Use theme variable (15px is close to 16px)
}

.time-fields {
  display: flex;
  gap: var(--spacing-sm); // Use theme variable (10px is between sm and md)
  margin-bottom: var(--spacing-xs); // Use theme variable (5px is close to xs)

  .time-field {
    flex: 1;
    // Positional ::ng-deep tweaks might be removable if matTimepicker styles well by default

    /*
    ::ng-deep .mat-form-field-infix {
      padding-top: 0.5em;
    }

    ::ng-deep .mat-form-field-label-wrapper {
      top: -1em;
    }
    */
  }
}

.post-fields {
  display: flex;
  gap: var(--spacing-sm); // Use theme variable
  margin-bottom: var(--spacing-xs); // Use theme variable

  .post-field {
    flex: 1;
    // Positional ::ng-deep tweaks might be removable
    /*
    ::ng-deep .mat-form-field-infix {
      padding-top: 0.5em;
    }

    ::ng-deep .mat-form-field-label-wrapper {
      top: -1em;
    }
    */
  }
}

// Fix for dropdown menu styling and other global Material overrides within this component
:host ::ng-deep { // Use :host ::ng-deep for component-scoped global overrides
  .mat-mdc-select-panel { // Updated class for Material 15+
    background-color: var(--background-color) !important; 
    border-radius: var(--border-radius-sm); 
    box-shadow: var(--shadow-dp2); 
  }

  .mat-mdc-option { // Updated class for Material 15+
    color: var(--text-primary-color); 
    &.mat-mdc-option-active, &.mdc-list-item--selected { // Handle hover and selected states
       color: var(--accent-color); 
       background-color: rgba(var(--accent-color-rgb), 0.08) !important; // Ensure proper background for selected
    }
  }
  
  .mat-mdc-form-field-outline-thick { // Updated class for Material 15+
    color: var(--accent-color) !important; // Ensure accent focus unless primary is desired
  }

  // Remove native time input styling as we now use MatTimepickerModule
  /*
  input[type="time"] {
    font-family: var(--font-family-base); 
    font-size: var(--font-size-md); 
    padding: var(--spacing-sm) 0; 
    width: 100%;
    color: var(--text-primary-color); 

    &::-webkit-calendar-picker-indicator {
      filter: invert(0.5); 
      cursor: pointer;
      opacity: 0.6;

      &:hover {
        opacity: 1;
      }
    }
  }
  */

  // Dialog styling (MatDialog applies its own panel class, these might not be needed or need to target .mat-mdc-dialog-container)
  .mat-mdc-dialog-container .mdc-dialog__surface { // More specific selector for Material 15+
    border-radius: var(--border-radius-md) !important; 
    box-shadow: var(--shadow-dp8) !important; 
  }

  .mat-mdc-dialog-title { // Updated class for Material 15+
    margin-bottom: var(--spacing-sm); 
    color: var(--primary-color); 
    font-weight: var(--font-weight-medium); 
    padding-bottom: var(--spacing-xs); 
    border-bottom: 1px solid var(--border-color); 
  }

  .mat-mdc-dialog-actions { // Updated class for Material 15+
    padding: var(--spacing-sm) var(--spacing-lg) !important; // Override default padding if necessary
    margin-bottom: 0;
    display: flex; // Ensure flex for alignment
    justify-content: flex-end; // Align to end

    // Button styling within dialog actions
    button.mat-mdc-button, button.mat-mdc-raised-button, button.mat-mdc-stroked-button { // General targeting for all Material button types
      margin-left: var(--spacing-sm); // Add left margin to space out buttons
      &:first-child {
        margin-left: 0; // No left margin for the first button
      }
    }

    // Default button (Cancel - assuming it's a mat-stroked-button or mat-button)
    button[mat-button], button[mat-stroked-button] {
      &:not([color="primary"]):not([color="accent"]):not([color="warn"]) { // Target non-palette colored buttons
        // Styles for a "default" or "cancel" button can go here if mat-button default is not enough
        // For example, to make it look more like the old one:
        // background-color: var(--surface-color);
        // color: var(--text-primary-color);
        // border: 1px solid var(--border-color);
      }
    }
    // Save button is already mat-raised-button color="primary", should be themed correctly.
    // Delete button is already mat-button color="warn", should be themed correctly.
  }
}

@media (max-width: 600px) {
  mat-dialog-content {
    min-width: 280px; 
    padding: var(--spacing-md); 
  }

  .time-fields,
  .post-fields {
    flex-direction: column;
    gap: var(--spacing-sm); // Use theme variable
  }
}
