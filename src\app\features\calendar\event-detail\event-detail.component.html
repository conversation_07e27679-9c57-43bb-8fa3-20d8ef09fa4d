<h2 mat-dialog-title>{{ data.isHoliday ? 'Holiday/Event Details' : 'Time Entry Details' }}</h2>

<div mat-dialog-content>
  <div *ngIf="isLoading" class="loading-indicator">Loading...</div>
  <div *ngIf="errorMessage" class="error-message">{{ errorMessage }}</div>

  <form [formGroup]="eventForm" (ngSubmit)="onSubmit()">
    <!-- Holiday Event Form -->
    <div *ngIf="data.isHoliday" class="holiday-form">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Event Name</mat-label>
        <input matInput formControlName="event_name" required>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="holidayPicker" formControlName="date" required>
        <mat-datepicker-toggle matSuffix [for]="holidayPicker"></mat-datepicker-toggle>
        <mat-datepicker #holidayPicker></mat-datepicker>
      </mat-form-field>
    </div>

    <!-- Time Entry Form -->
    <div *ngIf="!data.isHoliday" class="time-entry-form">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Entry Type</mat-label>
        <mat-select formControlName="entry_type" required>
          <mat-option *ngFor="let type of entryTypes" [value]="type.value">
            {{ type.label }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="datePicker" formControlName="date" required>
        <mat-datepicker-toggle matSuffix [for]="datePicker"></mat-datepicker-toggle>
        <mat-datepicker #datePicker></mat-datepicker>
      </mat-form-field>

      <div class="time-fields">
        <mat-form-field appearance="outline" class="time-field">
          <mat-label>Start Time</mat-label>
          <input matInput [matTimepicker]="startTimePicker" formControlName="startTime" required placeholder="HH:MM">
          <mat-timepicker-toggle matSuffix [for]="startTimePicker"></mat-timepicker-toggle>
          <mat-timepicker #startTimePicker interval="15m"></mat-timepicker> <!-- Interval can be adjusted -->
           <mat-error *ngIf="eventForm.get('startTime')?.hasError('required')">Start time is required.</mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="time-field">
          <mat-label>End Time</mat-label>
          <input matInput [matTimepicker]="endTimePicker" formControlName="endTime" required placeholder="HH:MM">
          <mat-timepicker-toggle matSuffix [for]="endTimePicker"></mat-timepicker-toggle>
          <mat-timepicker #endTimePicker interval="15m"></mat-timepicker> <!-- Interval can be adjusted -->
          <mat-error *ngIf="eventForm.get('endTime')?.hasError('required')">End time is required.</mat-error>
        </mat-form-field>
      </div>
      <div *ngIf="eventForm.hasError('endTimeBeforeStart')" class="error-message"> <!-- Assuming you might add a similar validator -->
        <small>End time must be after start time.</small>
      </div>

      <div class="post-fields">
        <mat-form-field appearance="outline" class="post-field">
          <mat-label>Post Number</mat-label>
          <input matInput formControlName="post_number">
        </mat-form-field>

        <mat-form-field appearance="outline" class="post-field">
          <mat-label>Post Description</mat-label>
          <input matInput formControlName="post_description">
        </mat-form-field>
      </div>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Notes</mat-label>
        <textarea matInput formControlName="notes" rows="3"></textarea>
      </mat-form-field>
    </div>
  </form>
</div>

<div mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">Cancel</button>
  <button mat-button color="warn" (click)="onDelete()" *ngIf="!data.isReadOnly && eventForm.value.id">Delete</button>
  <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="eventForm.invalid || isLoading" *ngIf="!data.isReadOnly">Save</button>
</div>
