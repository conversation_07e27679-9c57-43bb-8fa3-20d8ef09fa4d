<div class="dashboard-page-container">
  <div class="dashboard-header">
    <h2 class="dashboard-title">Dashboard</h2>
  </div>

  @if (isLoading()) {
    <div class="dashboard-loader">
      <mat-progress-spinner mode="indeterminate" diameter="50"></mat-progress-spinner>
      <p>Loading dashboard data...</p>
    </div>
  }

  @if (errorMessage()) {
    <div class="dashboard-alert error-alert">
      <mat-icon>error_outline</mat-icon>
      <span>{{ errorMessage() }}</span>
    </div>
  }

  @if (!isLoading() && !errorMessage()) {
    <div class="content-fade-in"> <!-- Apply fade-in to this wrapper -->
      <!-- Pay Period Summary Section -->
      <app-pay-period-stats></app-pay-period-stats>

      <!-- Overtime Summary Section -->
      <app-overtime-summary></app-overtime-summary>

      <!-- Action Buttons -->
      <div class="dashboard-actions">
        <button mat-stroked-button color="primary" (click)="openView998FormatDialog()">
          <mat-icon>description</mat-icon> View in 998 Format
        </button>
        <button mat-stroked-button color="primary" (click)="openOvertimeCalculator()">
          <mat-icon>calculate</mat-icon> Overtime Pay Calculator
        </button>
      </div>
    </div>
  }
</div>