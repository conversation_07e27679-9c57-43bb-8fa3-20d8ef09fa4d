// src/app/features/dashboard/pay-period-stats/pay-period-stats.component.scss

.pay-period-stats-card {
  // Uses default mat-card styling
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.summary-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary-color);
  display: flex;
  align-items: center;
}

.summary-title-tooltip {
  font-size: var(--font-size-lg);
  color: var(--text-secondary-color);
  margin-left: var(--spacing-xs);
  cursor: help;
  &:hover {
    color: var(--text-primary-color);
  }
}

.summary-nav {
  display: flex;
  align-items: center;
}

.summary-nav-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary-color);
  margin: 0 var(--spacing-xs);
}

.period-indicator {
  text-align: center;
  margin-bottom: var(--spacing-md);
}

.period-indicator-text {
  background-color: var(--surface-color);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

.summary-loader {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  gap: var(--spacing-sm);
  color: var(--text-secondary-color);
  font-size: var(--font-size-md);
}

.summary-alert {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-md);
  border-left-width: 4px;
  border-left-style: solid;

  mat-icon {
    margin-right: var(--spacing-sm);
  }
  span {
    font-size: var(--font-size-md);
  }

  &.error-alert {
    background-color: rgba(var(--error-color-rgb), 0.1);
    border-left-color: var(--error-color);
    color: var(--error-color);
    mat-icon {
      color: var(--error-color);
    }
  }
}

.stats-container {
  // Container for all stats sections
}

.stats-grid {
  display: grid;
  gap: var(--spacing-md);

  &.main-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); // Adjusted minmax for 2 items
    margin-bottom: var(--spacing-lg); // Space before leave usage section
  }

  &.leave-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); // Adjusted for 3 items potentially
  }
}

.stat-item {
  background-color: var(--surface-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-dp0);
  transition: box-shadow 0.3s ease;
  text-align: left; // Align text to left for items

  &:hover {
    box-shadow: var(--shadow-dp2);
  }
}

.stat-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary-color);
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
}

.stat-tooltip {
  font-size: var(--font-size-md);
  color: var(--text-secondary-color);
  margin-left: var(--spacing-xxs);
  cursor: help;
  &:hover {
    color: var(--text-primary-color);
  }
}

.stat-value {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary-color);
}

.leave-usage-section {
  margin-top: var(--spacing-lg);
}

.leave-usage-title {
  font-size: var(--font-size-lg); // Slightly smaller than main card title
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary-color);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
}

.no-data-message {
  font-size: var(--font-size-md);
  color: var(--text-secondary-color);
  text-align: center;
  padding: var(--spacing-md) 0;
}


// Responsive adjustments
@media (max-width: 768px) {
  .summary-header {
    flex-direction: column;
    align-items: flex-start;
  }
  .summary-nav {
    margin-top: var(--spacing-sm);
  }
  .stats-grid.main-stats-grid,
  .stats-grid.leave-stats-grid {
    grid-template-columns: 1fr; // Stack all stats on smaller screens
  }
}
