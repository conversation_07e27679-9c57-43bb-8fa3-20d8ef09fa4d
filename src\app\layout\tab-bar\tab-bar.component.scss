.tab-bar-nav {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 60px; // Standard mobile tab bar height
  width: 100%;
  background-color: var(--primary-color);
  color: var(--on-primary-color);
  box-shadow: var(--shadow-dp2); // Use a subtle shadow
  position: fixed; // Fixed at the bottom for mobile
  bottom: 0;
  left: 0;
  z-index: 1000; // Ensure it's above other content on mobile
}

.tab-bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 100%;
  color: var(--on-primary-color);
  text-decoration: none;
  transition: background-color 0.3s ease;
  position: relative; // For the active indicator ::after pseudo-element

  &:hover {
    background-color: rgba(var(--on-primary-color-rgb), 0.08); // Subtle hover like sidebar
  }

  &.active-link {
    background-color: rgba(var(--on-primary-color-rgb), 0.15); // Slightly more prominent active state like sidebar
    // color: var(--accent-color); // Alternative: change icon/text color to accent

    .tab-bar-label {
      font-weight: var(--font-weight-medium);
    }
    
    // Active indicator line
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40%; // Adjust width as needed
      height: 3px;
      background-color: var(--accent-color);
      border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
    }
  }

  mat-icon {
    font-size: 24px; // Standard Material Icon size
    height: 24px;
    width: 24px;
    margin-bottom: var(--spacing-xs); // 4px
  }

  .tab-bar-label {
    font-size: var(--font-size-xs); // 12px
    font-weight: var(--font-weight-normal); // Ensure default is normal
  }
}

// This component is typically only shown on mobile.
// The visibility control (display: none on desktop) will be in app.component.scss
// or handled by *ngIf based on screen size in app.component.ts if preferred.