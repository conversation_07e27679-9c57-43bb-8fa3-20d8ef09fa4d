// src/app/auth/signup/signup.component.ts
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { SupabaseService } from '../../core/services/supabase.service';
import { CommonModule } from '@angular/common';

// Angular Material Modules
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-signup',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterLink,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatIconModule
  ],
  templateUrl: './signup.component.html',
  styleUrls: ['./signup.component.scss'] // Added styleUrls
})
export class SignupComponent implements OnInit {
  signupForm: FormGroup;
  isLoading = false;
  errorMessage: string | null = null;
  successMessage: string | null = null;

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    private router: Router
  ) {
    // Initialize the form group
    this.signupForm = this.fb.group({
      // You might add username later if needed for profile trigger
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      // confirmPassword: ['', Validators.required] // Example if adding confirmation
    }
    // , { validators: this.passwordMatchValidator } // Example if adding confirmation
    );
  }

  ngOnInit(): void {}

  // Optional: Validator for password confirmation
  // passwordMatchValidator(form: FormGroup) { ... }

  // Method to handle form submission
  async onSubmit(): Promise<void> {
    // console.log('Signup onSubmit triggered.'); // <-- REMOVED
    if (this.signupForm.invalid) {
       this.errorMessage = 'Please fill out the form correctly.';
       // console.log('Signup form invalid.'); // <-- REMOVED
      return;
    }
    // console.log('Signup form valid. Setting loading state.'); // <-- REMOVED

    this.isLoading = true;
    this.errorMessage = null;
    this.successMessage = null;
    const email = this.signupForm.value.email;
    const password = this.signupForm.value.password;

    try {
      // Get the current URL's origin (e.g., http://localhost:4200)
      const redirectUrl = window.location.origin + '/auth/verification-success';

      // Attempt signup using the Supabase service
      const { data, error } = await this.supabaseService.auth.signUp({
        email: email,
        password: password,
        options: {
          emailRedirectTo: redirectUrl,
          data: {
            email: email // Include email in user metadata
          }
        }
      });
       // console.log('Supabase signUp call returned:', { data, error }); // <-- REMOVED

      if (error) {
        throw error; // Let the catch block handle Supabase errors
      }

      // Handle different outcomes based on Supabase Auth settings
      if (data.user && data.user.identities?.length === 0) {
         this.errorMessage = 'Signup process incomplete. Please try again or contact support.';
      } else if (data.session) {
        // Email confirmation likely OFF - user is logged in
        this.successMessage = 'Signup successful! Redirecting...';
         // console.log('Signup success, session exists, navigating...'); // <-- REMOVED
        // TODO: Replace '/dashboard' with the actual target route later
        this.router.navigate(['/dashboard']);
      } else if (data.user) {
         // Email confirmation likely ON - user created, needs verification
        this.successMessage = 'Signup successful! Please check your email to confirm your account.';
         // console.log('Signup success, user created, email confirmation likely needed.'); // <-- REMOVED
        this.signupForm.reset(); // Clear form on success
      } else {
         // Unexpected case
         this.errorMessage = 'An unexpected issue occurred during signup.';
         // console.log('Signup unexpected outcome (no error, no session, no user).'); // <-- REMOVED
      }

    } catch (error: any) {
      // Log the error caught in the catch block - KEEP THIS
      console.error('Signup catch block error:', error);
      this.errorMessage = error.message || 'An unexpected error occurred during signup.';
    } finally {
      this.isLoading = false; // Reset loading state
      // console.log('Signup onSubmit finished.'); // <-- REMOVED
    }
  }

  // Getters for easy access to form controls in the template
  get email() { return this.signupForm.get('email'); }
  get password() { return this.signupForm.get('password'); }
  // get confirmPassword() { return this.signupForm.get('confirmPassword'); }
}