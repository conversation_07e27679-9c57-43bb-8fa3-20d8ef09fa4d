.calendar-container {
  padding: var(--spacing-md); // Updated
  background-color: var(--background-color); // Updated
  border-radius: var(--border-radius-md); // Updated
  box-shadow: var(--shadow-dp1); // Updated
  margin-bottom: var(--spacing-lg); // Updated
}

.calendar-wrapper {
  margin-top: 15px;
}

.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-lg); // Updated
  font-style: italic;
  color: var(--text-secondary-color); // Updated
}

.error-message {
  background-color: rgba(var(--error-color-rgb), 0.1); // Updated
  color: var(--error-color); // Updated
  padding: var(--spacing-sm); // Updated
  border-radius: var(--border-radius-sm); // Updated
  margin-bottom: var(--spacing-md); // Updated
}

// FullCalendar customizations
:host ::ng-deep {
  // Header styling
  .fc-toolbar-title {
    font-size: var(--font-size-xl); // Updated
    color: var(--primary-color); // Updated
  }

  // Button styling
  .fc-button-primary { // Default FullCalendar button class
    background-color: var(--primary-color); // Updated
    border-color: var(--primary-color); // Updated
    color: var(--on-primary-color); // Added for text on primary
  }

  .fc-button-primary:hover {
    background-color: var(--primary-color-light); // Use defined lighter shade
    border-color: var(--primary-color-light); // Use defined lighter shade
    color: var(--on-primary-color); // Ensure text contrast on hover
  }

  .fc-button-primary:not(:disabled).fc-button-active,
  .fc-button-primary:not(:disabled):active {
    background-color: var(--accent-color); // Updated
    border-color: var(--accent-color); // Updated
    color: var(--on-accent-color); // Updated for text on accent
  }

  // Day header styling
  .fc-col-header-cell {
    background-color: var(--primary-color); // Updated
    color: var(--on-primary-color); // Updated
    padding: var(--spacing-sm) 0; // Updated
  }

  // Event styling
  .fc-event { // Default FullCalendar event class
    cursor: pointer;
    border-radius: var(--border-radius-sm); // Updated
    font-size: var(--font-size-sm); // Updated
    // FullCalendar typically handles event background and text color.
    // If overrides are needed:
    // background-color: var(--primary-color);
    // color: var(--on-primary-color);
    // border-color: var(--primary-color);
  }

  // Style for event titles to handle overflow
  .fc-event-title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // Non-clickable events (OT periods, paydays, holidays)
  .fc-event[data-event-type="ot-period"],
  .fc-event[data-event-type="payday"],
  .fc-event[data-event-type="holiday"] {
    cursor: default;
  }

  // Today highlight
  .fc-day-today {
    background-color: rgba(var(--primary-color-rgb), 0.1) !important; // Updated
  }

  // Holiday event styling with blue glowing effect
  .holiday-event {
    position: relative;
    z-index: 1;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
      border-radius: var(--border-radius-sm); // Updated
      animation: blueGlow 2s infinite alternate;
    }
  }

  // Payday event styling with gold glowing effect
  .payday-event {
    position: relative;
    z-index: 1;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
      border-radius: var(--border-radius-sm); // Updated
      animation: goldGlow 2s infinite alternate;
    }
  }

  // Media query for smaller screens (mobile responsiveness)
  @media (max-width: 768px) {
    .fc-toolbar-title {
      font-size: var(--font-size-lg) !important; // Updated (1.1rem is close to lg)
    }

    .fc-event-title { // Reduce event title font size
      font-size: var(--font-size-xs) !important; // Updated (0.8em is between xs and sm)
    }

    .fc-button {
      padding: var(--spacing-xs) var(--spacing-sm) !important; // Updated (0.3em 0.5em)
      // The button text is already shortened by TypeScript for mobile
    }

    // Ensure the shortened button texts are vertically centered if needed
    .fc-button .fc-button-text { // Or directly .fc-button if icons are not used
      line-height: 1.5; // Adjust as needed for vertical alignment
    }
  }
}

// Blue glow animation for holidays
@keyframes blueGlow {
  from {
    box-shadow: 0 0 5px rgba(var(--holiday-glow-color-rgb), 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(var(--holiday-glow-color-rgb), 0.9), 0 0 30px rgba(var(--holiday-glow-color-rgb), 0.7);
  }
}

// Gold glow animation for paydays (exaggerated as requested)
@keyframes goldGlow {
  from {
    box-shadow: 0 0 10px rgba(var(--payday-glow-color-rgb), 0.6);
  }
  to {
    box-shadow: 0 0 25px rgba(var(--payday-glow-color-rgb), 0.9), 0 0 40px rgba(var(--payday-glow-color-rgb), 0.7);
  }
}