import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router, NavigationEnd, Event } from '@angular/router';
import { SupabaseService } from '../../../core/services/supabase.service';
import { Subscription, filter } from 'rxjs';

@Component({
  selector: 'app-search-bar',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    FormsModule,
    ReactiveFormsModule
  ],
  templateUrl: './search-bar.component.html',
  styleUrl: './search-bar.component.scss'
})
export class SearchBarComponent implements OnInit, OnDestroy {
  isAuthenticated = false;
  isCalendarRoute = false;
  searchQuery = '';
  private authSubscription: Subscription | null = null;
  private routerSubscription: Subscription | null = null;

  constructor(
    private supabaseService: SupabaseService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Subscribe to authentication state changes
    this.authSubscription = this.supabaseService.currentUser$.subscribe(user => {
      this.isAuthenticated = !!user && typeof user !== 'boolean';
    });

    // Subscribe to router events to check if we're on the calendar route
    this.routerSubscription = this.router.events.pipe(
      filter((event: Event): event is NavigationEnd => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.isCalendarRoute = event.urlAfterRedirects === '/calendar';
    });

    // Check initial route
    this.isCalendarRoute = this.router.url === '/calendar';
  }

  ngOnDestroy(): void {
    // Clean up subscriptions when component is destroyed
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }

  /**
   * Determines if the search bar should be visible
   * Only visible when user is authenticated AND on the calendar route
   */
  get isVisible(): boolean {
    return this.isAuthenticated && this.isCalendarRoute;
  }

  onSearch(): void {
    // This will be implemented in a future task
    console.log('Search query:', this.searchQuery);
  }
}
