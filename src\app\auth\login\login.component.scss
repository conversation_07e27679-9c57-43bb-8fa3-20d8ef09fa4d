// src/app/auth/login/login.component.scss

.login-page-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 64px); // Assuming 64px is toolbar height, adjust if needed
  padding: var(--spacing-md);
  background-color: var(--surface-color); // Page background
  box-sizing: border-box;
}

.login-card {
  width: 100%;
  max-width: 400px; // Max width for the login form card
  padding: var(--spacing-md) 0; // Add some vertical padding if mat-card-content doesn't suffice

  .mat-mdc-card-header {
    padding: 0 var(--spacing-lg) var(--spacing-md) var(--spacing-lg); // Adjust header padding
  }
  .mat-mdc-card-content {
    padding: 0 var(--spacing-lg) var(--spacing-lg) var(--spacing-lg); // Adjust content padding
  }
   .mat-mdc-card-actions {
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: center;
  }
}

.login-title {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary-color);
  text-align: center;
  width: 100%; // Ensure it takes full width of card header
}

.full-width-field {
  width: 100%;
  margin-bottom: var(--spacing-sm); // Reduced margin for tighter form
}

.server-message {
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;

  mat-icon {
    margin-right: var(--spacing-xs);
    font-size: var(--font-size-lg); // Adjust icon size
  }

  &.error-message {
    background-color: rgba(var(--error-color-rgb), 0.1);
    color: var(--error-color);
    border-left: 3px solid var(--error-color);
    mat-icon { color: var(--error-color); }
  }

  &.success-message {
    background-color: rgba(var(--success-color-rgb), 0.1);
    color: var(--success-color);
    border-left: 3px solid var(--success-color);
    mat-icon { color: var(--success-color); }
  }
}

.resend-button {
  margin-top: var(--spacing-sm);
  display: block; // Make it block to take full width or adjust as needed
  width: 100%;
  text-transform: none; // Prevent uppercase styling from mat-button if not desired
  font-size: var(--font-size-sm);
}

.full-width-button {
  width: 100%;
  padding: var(--spacing-sm) 0; // Adjust padding for button height
  font-size: var(--font-size-md); // Ensure consistent font size
}

.login-submit-button {
  margin-top: var(--spacing-sm); // Space above the main submit button
}

.button-spinner {
  margin-right: var(--spacing-sm);
  // Diameter is set on the element
}

.login-card-actions p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary-color);
}

.nav-link {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  &:hover {
    text-decoration: underline;
    color: var(--accent-color);
  }
}

// Responsive adjustments
@media (max-width: 600px) {
  .login-card {
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    padding: 0; // Remove card padding if page padding is enough
  }
  .login-page-container {
     min-height: calc(100vh - 56px); // Assuming 56px is mobile toolbar height
  }
}