// src/app/features/settings/settings.component.ts
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { SupabaseService, Profile } from '../../core/services/supabase.service';
import { User } from '@supabase/supabase-js';
import { Subscription } from 'rxjs';

// Angular Material Modules
import { MatCardModule } from '@angular/material/card';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon'; // For icons in expansion panel or messages

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatIconModule
  ],
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SettingsComponent implements OnInit, OnDestroy {
  currentUser: User | null | boolean = null;
  profile: Profile | null = null;
  isLoading = false;
  errorMessage: string | null = null;
  successMessage: string | null = null;



  // Contact form
  contactForm: FormGroup;
  isContactSubmitting = false;
  contactErrorMessage: string | null = null;
  contactSuccessMessage: string | null = null;
  // isContactFormVisible = false; // Removed, MatExpansionPanel handles its own state

  // Subscriptions
  authSubscription: Subscription | null = null;

  constructor(
    private supabaseService: SupabaseService,
    private router: Router,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef
  ) {


    // Initialize contact form
    this.contactForm = this.fb.group({
      subject: ['', Validators.required],
      message: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    // isLoading = true was removed here, set in loadProfile
    this.authSubscription = this.supabaseService.currentUser$.subscribe(user => {
      this.currentUser = user;
      if (user && typeof user !== 'boolean') {
        this.loadProfile(user.id);
      } else {
        this.profile = null;
        this.isLoading = false; // Ensure loading stops if no user
        this.cdr.markForCheck();
      }
    });
  }

  ngOnDestroy(): void {
    this.authSubscription?.unsubscribe();
  }

  async loadProfile(userId: string): Promise<void> {
     this.isLoading = true; // Set loading true when starting load
     this.errorMessage = null;
     this.successMessage = null;
     this.cdr.markForCheck(); // Update view for loading state
    try {
      const fetchedProfile = await this.supabaseService.getProfile(userId);
      if (fetchedProfile) {
         this.profile = fetchedProfile;
      } else {
        this.errorMessage = "Could not load user profile.";
        this.profile = null;
      }
    } catch (error: any) {
      console.error("Error loading profile:", error);
      this.errorMessage = error.message || "Failed to load profile.";
      this.profile = null;
    } finally {
       this.isLoading = false;
       this.cdr.markForCheck(); // Update view after loading finishes
    }
  }



  async handleLogout(): Promise<void> {
      this.isLoading = true;
      this.cdr.markForCheck();
      try { await this.supabaseService.auth.signOut(); this.router.navigate(['/auth/login']); }
      catch (error: any) { console.error("Logout failed:", error); this.errorMessage = error.message || "Logout failed."; this.isLoading = false; this.cdr.markForCheck();}
   }

  async onSubmitContactForm(): Promise<void> {
    if (!this.currentUser || typeof this.currentUser === 'boolean') {
      this.contactErrorMessage = 'You must be logged in to submit a message.';
      this.cdr.markForCheck();
      return;
    }

    this.contactForm.markAllAsTouched();
    if (this.contactForm.invalid) {
      this.contactErrorMessage = 'Please fill out all required fields.';
      this.cdr.markForCheck();
      return;
    }

    this.isContactSubmitting = true;
    this.contactErrorMessage = null;
    this.contactSuccessMessage = null;
    this.cdr.markForCheck();

    try {
      const userId = this.currentUser.id;
      const email = this.currentUser.email || '';
      const { subject, message } = this.contactForm.value;

      await this.supabaseService.submitContactMessage({
        user_id: userId,
        email,
        subject,
        message
      });

      this.contactSuccessMessage = 'Your message has been sent successfully!';
      this.contactForm.reset();
      this.contactForm.markAsPristine();
      this.contactForm.markAsUntouched();
    } catch (error: any) {
      console.error('Error submitting contact message:', error);
      this.contactErrorMessage = error.message || 'Failed to send message. Please try again later.';
    } finally {
      this.isContactSubmitting = false;
      this.cdr.markForCheck();
    }
  }

  navigateToAdmin(): void {
    if (this.profile?.is_admin) {
      this.router.navigate(['/admin']);
    }
  }

  get userDisplayEmail(): string {
    if (this.currentUser && typeof this.currentUser !== 'boolean') {
      return this.currentUser.email ?? 'N/A';
    }
    return 'N/A';
  }



  // Contact form getters
  get subject() { return this.contactForm.get('subject'); }
  get message() { return this.contactForm.get('message'); }

  // Toggle contact form visibility - Removed as MatExpansionPanel handles its own state
  // toggleContactForm(): void {
  //   this.isContactFormVisible = !this.isContactFormVisible;
  //   this.cdr.markForCheck();
  // }
}