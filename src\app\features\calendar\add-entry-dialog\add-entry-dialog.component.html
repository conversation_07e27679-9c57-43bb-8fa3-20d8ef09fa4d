<h2 mat-dialog-title>Add New Entry</h2>

<div mat-dialog-content class="dialog-content">
  <div *ngIf="errorMessage && !successMessage" class="error-message">{{ errorMessage }}</div>
  <div *ngIf="successMessage" class="success-message">{{ successMessage }}</div>

  <!-- Note: removed ngSubmit to prevent form submission conflicts -->
  <form [formGroup]="entryForm" class="entry-form">
    <div class="form-group date-field-container">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Date</mat-label>
        <input matInput [matDatepicker]="datePicker" formControlName="entryDate" required>
        <mat-datepicker-toggle matSuffix [for]="datePicker" class="date-picker-toggle-fix">
        </mat-datepicker-toggle>
        <mat-datepicker #datePicker></mat-datepicker>
        <mat-error *ngIf="entryDate?.invalid && entryDate?.touched">
          Date is required
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-group">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Entry Type</mat-label>
        <mat-select formControlName="entryType" required>
          <mat-option *ngFor="let type of entryTypes" [value]="type.value">
            {{ type.label }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="entryType?.invalid && entryType?.touched">
          Entry type is required
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-group time-method-selector">
      <label class="time-method-label">Time Input Method:</label>
      <div>
        <mat-radio-group formControlName="timeInputMethod">
          <mat-radio-button value="watch">Watch</mat-radio-button>
          <mat-radio-button value="custom">Custom Times</mat-radio-button>
        </mat-radio-group>
      </div>
    </div>

    <div class="form-group" *ngIf="timeInputMethod?.value === 'watch'">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Select Watch</mat-label>
        <mat-select formControlName="watchSelection">
          <mat-option *ngFor="let watch of watches" [value]="watch">
            {{ watch.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="watchSelection?.invalid && watchSelection?.touched">
          Watch selection is required
        </mat-error>
      </mat-form-field>
    </div>

    <div class="form-group custom-times" *ngIf="timeInputMethod?.value === 'custom'">
      <div class="time-inputs-container">
        <mat-form-field appearance="outline" class="time-field">
          <mat-label>Start Time</mat-label>
          <input matInput [matTimepicker]="startTimePicker" formControlName="startTime" required placeholder="HH:MM">
          <mat-timepicker-toggle matSuffix [for]="startTimePicker"></mat-timepicker-toggle>
          <mat-timepicker #startTimePicker interval="30m"></mat-timepicker>
          <mat-error *ngIf="startTime?.invalid && startTime?.touched">
            Start time is required
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="time-field">
          <mat-label>End Time</mat-label>
          <input matInput [matTimepicker]="endTimePicker" formControlName="endTime" required placeholder="HH:MM">
          <mat-timepicker-toggle matSuffix [for]="endTimePicker"></mat-timepicker-toggle>
          <mat-timepicker #endTimePicker interval="30m"></mat-timepicker>
          <mat-error *ngIf="endTime?.invalid && endTime?.touched">
            End time is required
          </mat-error>
        </mat-form-field>
      </div>

      <div *ngIf="entryForm.hasError('endTimeBeforeStart')" class="error-message">
        <small>End time must be after start time</small>
      </div>
    </div>

    <div class="post-fields">
      <mat-form-field appearance="outline">
        <mat-label>Post Number (Optional)</mat-label>
        <input matInput formControlName="post_number">
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Post Description (Optional)</mat-label>
        <input matInput formControlName="post_description">
      </mat-form-field>
    </div>

    <!-- Mandatory Hold Checkbox - only shown for OT_MANDATED entry type -->
    <div class="form-group" *ngIf="entryType?.value === 'OT_MANDATED'">
      <mat-checkbox formControlName="counts_towards_mandatory">
        Count towards mandatory holdover total?
      </mat-checkbox>
      <div class="checkbox-hint">
        <small>Check this box if this mandated overtime should count towards your monthly mandatory holdover count.</small>
      </div>
    </div>

    <mat-form-field appearance="outline" class="full-width">
      <mat-label>Notes (Optional)</mat-label>
      <textarea matInput formControlName="notes" rows="3"></textarea>
    </mat-form-field>
  </form>
</div>

<div mat-dialog-actions align="end">
  <div *ngIf="isLoading" class="loading-indicator">Processing...</div>
  <div class="dialog-buttons">
    <button mat-button (click)="onCancel()" [disabled]="isLoading" type="button" #cancelButton>Cancel</button>
    <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="entryForm.invalid || isLoading" type="button">
      {{ isLoading ? 'Saving...' : 'Save Entry' }}
    </button>
  </div>
</div>
