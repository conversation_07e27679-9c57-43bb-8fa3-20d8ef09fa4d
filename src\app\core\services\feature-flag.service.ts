// src/app/core/services/feature-flag.service.ts
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, map, of, switchMap } from 'rxjs';
import { SupabaseService } from './supabase.service';

export interface AppModule {
  id: number;
  module_name: string;
  display_name: string;
  description: string | null;
  is_enabled: boolean;
  access_level: 'basic' | 'premium';
  depends_on: string[] | null;
  created_at: string;
  updated_at: string;
}

@Injectable({
  providedIn: 'root'
})
export class FeatureFlagService {
  private _modules = new BehaviorSubject<AppModule[]>([]);
  private _isLoading = new BehaviorSubject<boolean>(false);
  private _error = new BehaviorSubject<string | null>(null);

  constructor(private supabaseService: SupabaseService) {
    // Load modules when the service is initialized
    this.loadModules();
  }

  get modules$(): Observable<AppModule[]> {
    return this._modules.asObservable();
  }

  get isLoading$(): Observable<boolean> {
    return this._isLoading.asObservable();
  }

  get error$(): Observable<string | null> {
    return this._error.asObservable();
  }

  /**
   * Check if a feature is enabled for the current user
   * @param moduleName The name of the module to check
   * @returns Observable<boolean> True if the feature is enabled and accessible to the user
   */
  isFeatureEnabled(moduleName: string): Observable<boolean> {
    return this.supabaseService.currentUser$.pipe(
      switchMap(user => {
        // If user is not logged in, no features are available
        if (!user || typeof user === 'boolean') {
          return of(false);
        }

        // Get the user's profile to check subscription status
        return this.supabaseService.getProfile(user.id).then(profile => {
          if (!profile) {
            return false;
          }

          // Get the module from the cached modules
          const module = this._modules.getValue().find(m => m.module_name === moduleName);

          // If module doesn't exist or is not enabled, feature is not available
          if (!module || !module.is_enabled) {
            return false;
          }

          // If module is basic, it's available to all users
          if (module.access_level === 'basic') {
            return true;
          }

          // If module is premium, check user's subscription status
          return profile.subscription_status === 'premium';
        });
      })
    );
  }

  /**
   * Load all modules from the database
   */
  async loadModules(): Promise<void> {
    this._isLoading.next(true);
    this._error.next(null);

    try {
      const { data, error } = await this.supabaseService.supabaseInstance
        .from('app_modules')
        .select('*')
        .order('id', { ascending: true });

      if (error) {
        throw error;
      }

      this._modules.next(data as AppModule[]);
    } catch (error: any) {
      console.error('Error loading modules:', error);
      this._error.next(error.message || 'Failed to load modules');
    } finally {
      this._isLoading.next(false);
    }
  }

  /**
   * Update a module's settings
   * @param module The module to update
   * @returns Promise<AppModule | null> The updated module or null if update failed
   */
  async updateModule(module: Partial<AppModule> & { id: number }): Promise<AppModule | null> {
    this._isLoading.next(true);
    this._error.next(null);

    try {
      const { data, error } = await this.supabaseService.supabaseInstance
        .from('app_modules')
        .update(module)
        .eq('id', module.id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Update the cached modules
      const currentModules = this._modules.getValue();
      const updatedModules = currentModules.map(m =>
        m.id === module.id ? { ...m, ...data } : m
      );
      this._modules.next(updatedModules);

      return data as AppModule;
    } catch (error: any) {
      console.error('Error updating module:', error);
      this._error.next(error.message || 'Failed to update module');
      return null;
    } finally {
      this._isLoading.next(false);
    }
  }

  /**
   * Check if a module can be enabled/disabled based on dependencies
   * @param moduleId The ID of the module to check
   * @param newEnabledState The new enabled state to check
   * @returns Observable<{canChange: boolean, message: string | null}> Whether the change is allowed and a message if not
   */
  canChangeModuleState(moduleId: number, newEnabledState: boolean): Observable<{canChange: boolean, message: string | null}> {
    return this.modules$.pipe(
      map(modules => {
        const module = modules.find(m => m.id === moduleId);

        if (!module) {
          return { canChange: false, message: 'Module not found' };
        }

        // If enabling, check if dependencies are enabled
        if (newEnabledState && module.depends_on && module.depends_on.length > 0) {
          const missingDependencies = module.depends_on.filter(depName => {
            const dep = modules.find(m => m.module_name === depName);
            return !dep || !dep.is_enabled;
          });

          if (missingDependencies.length > 0) {
            return {
              canChange: false,
              message: `Cannot enable this module because it depends on: ${missingDependencies.join(', ')}`
            };
          }
        }

        // If disabling, check if other enabled modules depend on this one
        if (!newEnabledState) {
          const dependentModules = modules.filter(m =>
            m.is_enabled &&
            m.depends_on &&
            m.depends_on.includes(module.module_name)
          );

          if (dependentModules.length > 0) {
            return {
              canChange: false,
              message: `Cannot disable this module because these modules depend on it: ${dependentModules.map(m => m.display_name).join(', ')}`
            };
          }
        }

        return { canChange: true, message: null };
      })
    );
  }
}
