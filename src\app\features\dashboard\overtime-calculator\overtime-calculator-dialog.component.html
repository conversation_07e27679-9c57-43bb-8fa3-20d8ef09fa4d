<div class="overtime-calculator-container">
  <h2 mat-dialog-title>Overtime Pay Calculator</h2>
  <mat-dialog-content>
    <form [formGroup]="calculatorForm">
      <div class="form-container">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Your Gross Monthly Base Salary ($)</mat-label>
          <input
            matInput
            type="number"
            formControlName="monthlyBaseSalary"
            min="0"
            step="100"
            (input)="calculateOvertimePay()">
          <mat-hint>Enter the gross pay amount from your paystub</mat-hint>
          <mat-error *ngIf="calculatorForm.get('monthlyBaseSalary')?.hasError('required')">
            Monthly base salary is required
          </mat-error>
          <mat-error *ngIf="calculatorForm.get('monthlyBaseSalary')?.hasError('min')">
            Salary must be greater than 0
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Total OT Hours Worked in OT Period</mat-label>
          <input
            matInput
            type="number"
            formControlName="totalOvertimeHours"
            min="0"
            step="1"
            (input)="calculateOvertimePay()">
          <mat-error *ngIf="calculatorForm.get('totalOvertimeHours')?.hasError('required')">
            Total overtime hours is required
          </mat-error>
          <mat-error *ngIf="calculatorForm.get('totalOvertimeHours')?.hasError('min')">
            Hours must be greater than 0
          </mat-error>
        </mat-form-field>
      </div>
    </form>

    <mat-card class="results-card" *ngIf="calculatorForm.valid && calculatorForm.get('monthlyBaseSalary')?.value > 0">
      <mat-card-header>
        <mat-card-title>Overtime Pay Estimate</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="results-grid">
          <div class="result-item">
            <div class="result-label">Base Hourly Rate:</div>
            <div class="result-value">${{baseHourlyRate | number:'1.2-2'}}</div>
          </div>
          <div class="result-item">
            <div class="result-label">Overtime Pay Rate (1.5x):</div>
            <div class="result-value">${{overtimePayRate | number:'1.2-2'}}</div>
          </div>
          <mat-divider class="my-3"></mat-divider>
          <div class="result-item total">
            <div class="result-label">Estimated Gross Overtime Pay:</div>
            <div class="result-value">${{totalGrossOvertimePay | number:'1.2-2'}}</div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <div class="disclaimer">
      <p>
        <strong>Disclaimer:</strong> This calculator provides a simplified estimate of gross overtime pay based only on your base salary and overtime hours.
        It does not include pay differentials, deductions, or other factors. Actual overtime pay may vary.
      </p>
      <p>
        <strong>Calculation Method:</strong> Calculates your hourly rate based on your monthly salary, then applies the 1.5x overtime multiplier to
        determine your overtime pay rate. The total is calculated by multiplying your overtime hours by this rate.
      </p>
    </div>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button mat-button mat-dialog-close>Close</button>
  </mat-dialog-actions>
</div>
