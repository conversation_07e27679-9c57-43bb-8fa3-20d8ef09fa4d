// src/app/core/guards/admin.guard.ts
import { inject } from '@angular/core';
import { CanActivateFn, Router, UrlTree } from '@angular/router';
import { SupabaseService } from '../services/supabase.service';
import { Observable, from, map, of, take } from 'rxjs';
import { User } from '@supabase/supabase-js';

export const adminGuard: CanActivateFn = (route, state):
  | Observable<boolean | UrlTree>
  | Promise<boolean | UrlTree>
  | boolean
  | UrlTree => {

  const supabaseService = inject(SupabaseService);
  const router = inject(Router);

  return supabaseService.currentUser$.pipe(
    take(1),
    map(user => {
      // Check if user is logged in
      if (!user || typeof user === 'boolean') {
        return router.createUrlTree(['/auth/login']);
      }

      // User is logged in, return the user object
      return user as User;
    }),
    map(result => {
      // If result is a UrlTree, return it directly
      if (result instanceof UrlTree) {
        return result;
      }

      // Otherwise, it's a User object, check if admin
      const user = result as User;

      // We need to check synchronously here, so we'll redirect to dashboard
      // and let the AdminComponent handle the actual admin check
      return true;
    })
  );
};
