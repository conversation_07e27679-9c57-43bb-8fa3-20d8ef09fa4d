<div class="admin-container"> <!-- Removed Tailwind classes, will style with SCSS -->
  <h2 class="admin-title">Admin Panel</h2> <!-- Added class for styling -->

  <div *ngIf="isLoading" class="loading-spinner-container">
    <mat-progress-spinner mode="indeterminate" diameter="50"></mat-progress-spinner>
    <p>Loading...</p>
  </div>

  <mat-card *ngIf="errorMessage" class="message-card error-card" role="alert">
    <mat-card-content class="message-content">
      <mat-icon>error_outline</mat-icon>
      <span>{{ errorMessage }}</span>
    </mat-card-content>
  </mat-card>

  <mat-card *ngIf="successMessage" class="message-card success-card" role="alert">
    <mat-card-content class="message-content">
      <mat-icon>check_circle_outline</mat-icon>
      <span>{{ successMessage }}</span>
    </mat-card-content>
  </mat-card>

  <div *ngIf="!isLoading && isAdmin" class="content-fade-in"> <!-- Apply fade-in here -->
    <mat-tab-group class="admin-tab-group" [(selectedIndex)]="activeTabIndex" (selectedIndexChange)="handleTabChange($event)" animationDuration="0ms">
      <mat-tab label="OT Periods">
        <ng-template matTabContent>
          <!-- OT Periods Content will go here -->
          <div class="tab-content-wrapper">
            <h3 class="tab-section-title">Manage OT Periods</h3>
            <mat-card class="form-card">
              <mat-card-header>
                <mat-card-title>{{ editingOtPeriod ? 'Edit' : 'Add' }} OT Period</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <form [formGroup]="otPeriodForm" (ngSubmit)="onSubmitOtPeriod()" class="admin-form-material">
                  <mat-form-field appearance="outline" class="form-field-full-width">
                    <mat-label>Display Name (e.g., "May")</mat-label>
                    <input matInput formControlName="display_name" required>
                    <mat-hint>This name will be displayed on the calendar (e.g., "OT Begin: May")</mat-hint>
                    <mat-error *ngIf="otPeriodForm.get('display_name')?.hasError('required')">Display name is required.</mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="form-field-full-width">
                    <mat-label>Start Date</mat-label>
                    <input matInput [matDatepicker]="startDatePicker" formControlName="start_date" required>
                    <mat-datepicker-toggle matSuffix [for]="startDatePicker"></mat-datepicker-toggle>
                    <mat-datepicker #startDatePicker></mat-datepicker>
                    <mat-error *ngIf="otPeriodForm.get('start_date')?.hasError('required')">Start date is required.</mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="form-field-full-width">
                    <mat-label>End Date</mat-label>
                    <input matInput [matDatepicker]="endDatePicker" formControlName="end_date" required>
                    <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
                    <mat-datepicker #endDatePicker></mat-datepicker>
                    <mat-error *ngIf="otPeriodForm.get('end_date')?.hasError('required')">End date is required.</mat-error>
                  </mat-form-field>

                  <div *ngIf="otPeriodForm.hasError('endDateBeforeStart')" class="form-error-message">
                    End date must be after start date.
                  </div>

                  <div class="form-actions">
                    <button mat-raised-button color="primary" type="submit" [disabled]="otPeriodForm.invalid || isLoading">
                      <mat-icon>{{ editingOtPeriod ? 'save' : 'add_circle_outline' }}</mat-icon>
                      {{ editingOtPeriod ? 'Update' : 'Add' }} OT Period
                    </button>
                    <button mat-stroked-button type="button" *ngIf="editingOtPeriod" (click)="resetForms()" class="ml-2">
                      <mat-icon>cancel</mat-icon>
                      Cancel
                    </button>
                  </div>
                </form>
              </mat-card-content>
            </mat-card>

            <div class="table-container mat-elevation-z2">
              <table mat-table [dataSource]="otPeriodsDataSource" class="admin-mat-table">
                <!-- Display Name Column -->
                <ng-container matColumnDef="display_name">
                  <th mat-header-cell *matHeaderCellDef> Display Name </th>
                  <td mat-cell *matCellDef="let period"> {{ period.display_name || 'Not set' }} </td>
                </ng-container>

                <!-- Start Date Column -->
                <ng-container matColumnDef="start_date">
                  <th mat-header-cell *matHeaderCellDef> Start Date </th>
                  <td mat-cell *matCellDef="let period"> {{ formatDate(period.start_date) }} </td>
                </ng-container>

                <!-- End Date Column -->
                <ng-container matColumnDef="end_date">
                  <th mat-header-cell *matHeaderCellDef> End Date </th>
                  <td mat-cell *matCellDef="let period"> {{ formatDate(period.end_date) }} </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef> Actions </th>
                  <td mat-cell *matCellDef="let period">
                    <button mat-icon-button color="primary" (click)="editOtPeriod(period)" matTooltip="Edit OT Period">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" (click)="deleteOtPeriod(period.id)" matTooltip="Delete OT Period">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumnsOtPeriods; sticky: true"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumnsOtPeriods;"></tr>

                <tr class="mat-row" *matNoDataRow>
                  <td class="mat-cell" [attr.colspan]="displayedColumnsOtPeriods.length">
                    No OT periods found.
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </ng-template>
      </mat-tab>

      <mat-tab label="Holidays/Events">
        <ng-template matTabContent>
          <div class="tab-content-wrapper">
            <h3 class="tab-section-title">Manage Holidays & Events</h3>
            <mat-card class="form-card">
              <mat-card-header>
                <mat-card-title>{{ editingHolidayEvent ? 'Edit' : 'Add' }} Holiday/Event</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <form [formGroup]="holidayEventForm" (ngSubmit)="onSubmitHolidayEvent()" class="admin-form-material">
                  <mat-form-field appearance="outline" class="form-field-full-width">
                    <mat-label>Date</mat-label>
                    <input matInput [matDatepicker]="eventDatePicker" formControlName="event_date" required>
                    <mat-datepicker-toggle matSuffix [for]="eventDatePicker"></mat-datepicker-toggle>
                    <mat-datepicker #eventDatePicker></mat-datepicker>
                    <mat-error *ngIf="holidayEventForm.get('event_date')?.hasError('required')">Event date is required.</mat-error>
                  </mat-form-field>

                  <mat-form-field appearance="outline" class="form-field-full-width">
                    <mat-label>Event Name</mat-label>
                    <input matInput formControlName="event_name" required>
                    <mat-error *ngIf="holidayEventForm.get('event_name')?.hasError('required')">Event name is required.</mat-error>
                  </mat-form-field>

                  <div class="form-actions">
                    <button mat-raised-button color="primary" type="submit" [disabled]="holidayEventForm.invalid || isLoading">
                      <mat-icon>{{ editingHolidayEvent ? 'save' : 'add_circle_outline' }}</mat-icon>
                      {{ editingHolidayEvent ? 'Update' : 'Add' }} Holiday/Event
                    </button>
                    <button mat-stroked-button type="button" *ngIf="editingHolidayEvent" (click)="resetForms()" class="ml-2">
                      <mat-icon>cancel</mat-icon>
                      Cancel
                    </button>
                  </div>
                </form>
              </mat-card-content>
            </mat-card>

            <div class="table-container mat-elevation-z2">
              <table mat-table [dataSource]="holidayEventsDataSource" class="admin-mat-table">
                <!-- Date Column -->
                <ng-container matColumnDef="event_date">
                  <th mat-header-cell *matHeaderCellDef> Date </th>
                  <td mat-cell *matCellDef="let event"> {{ formatDate(event.event_date) }} </td>
                </ng-container>

                <!-- Event Name Column -->
                <ng-container matColumnDef="event_name">
                  <th mat-header-cell *matHeaderCellDef> Event Name </th>
                  <td mat-cell *matCellDef="let event"> {{ event.event_name }} </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef> Actions </th>
                  <td mat-cell *matCellDef="let event">
                    <button mat-icon-button color="primary" (click)="editHolidayEvent(event)" matTooltip="Edit Holiday/Event">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" (click)="deleteHolidayEvent(event.id)" matTooltip="Delete Holiday/Event">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumnsHolidays; sticky: true"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumnsHolidays;"></tr>
                
                <tr class="mat-row" *matNoDataRow>
                  <td class="mat-cell" [attr.colspan]="displayedColumnsHolidays.length">
                    No holidays or events found.
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </ng-template>
      </mat-tab>

      <mat-tab label="Payday Dates">
        <ng-template matTabContent>
          <div class="tab-content-wrapper">
            <h3 class="tab-section-title">Manage Payday Dates</h3>
            <mat-card class="form-card">
              <mat-card-header>
                <mat-card-title>{{ editingPaydayDate ? 'Edit' : 'Add' }} Payday Date</mat-card-title>
              </mat-card-header>
              <mat-card-content>
                <form [formGroup]="paydayDateForm" (ngSubmit)="onSubmitPaydayDate()" class="admin-form-material">
                  <mat-form-field appearance="outline" class="form-field-full-width">
                    <mat-label>Payday Date</mat-label>
                    <input matInput [matDatepicker]="paydayDatePicker" formControlName="payday_date" required>
                    <mat-datepicker-toggle matSuffix [for]="paydayDatePicker"></mat-datepicker-toggle>
                    <mat-datepicker #paydayDatePicker></mat-datepicker>
                    <mat-error *ngIf="paydayDateForm.get('payday_date')?.hasError('required')">Payday date is required.</mat-error>
                  </mat-form-field>

                  <div class="info-box mat-elevation-z1"> <!-- Using a class for styling this info box -->
                    <mat-icon class="info-icon">info</mat-icon>
                    <span>Pay periods are automatically set to the first day through the last day of each month based on the payday date.</span>
                  </div>

                  <div class="form-actions">
                    <button mat-raised-button color="primary" type="submit" [disabled]="paydayDateForm.invalid || isLoading">
                      <mat-icon>{{ editingPaydayDate ? 'save' : 'add_circle_outline' }}</mat-icon>
                      {{ editingPaydayDate ? 'Update' : 'Add' }} Payday Date
                    </button>
                    <button mat-stroked-button type="button" *ngIf="editingPaydayDate" (click)="resetForms()" class="ml-2">
                      <mat-icon>cancel</mat-icon>
                      Cancel
                    </button>
                  </div>
                </form>
              </mat-card-content>
            </mat-card>

            <div class="table-container mat-elevation-z2">
              <table mat-table [dataSource]="paydayDatesDataSource" class="admin-mat-table">
                <!-- Payday Date Column -->
                <ng-container matColumnDef="payday_date">
                  <th mat-header-cell *matHeaderCellDef> Payday Date </th>
                  <td mat-cell *matCellDef="let payday"> {{ formatDate(payday.payday_date) }} </td>
                </ng-container>

                <!-- Pay Period Start Column -->
                <ng-container matColumnDef="pay_period_start">
                  <th mat-header-cell *matHeaderCellDef> Period Start </th>
                  <td mat-cell *matCellDef="let payday"> {{ formatDate(payday.pay_period_start) }} </td>
                </ng-container>
                
                <!-- Pay Period End Column -->
                <ng-container matColumnDef="pay_period_end">
                  <th mat-header-cell *matHeaderCellDef> Period End </th>
                  <td mat-cell *matCellDef="let payday"> {{ formatDate(payday.pay_period_end) }} </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef> Actions </th>
                  <td mat-cell *matCellDef="let payday">
                    <button mat-icon-button color="primary" (click)="editPaydayDate(payday)" matTooltip="Edit Payday Date">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button color="warn" (click)="deletePaydayDate(payday.id)" matTooltip="Delete Payday Date">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumnsPaydays; sticky: true"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumnsPaydays;"></tr>

                <tr class="mat-row" *matNoDataRow>
                  <td class="mat-cell" [attr.colspan]="displayedColumnsPaydays.length">
                    No payday dates found.
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </ng-template>
      </mat-tab>

      <mat-tab label="App Modules">
        <ng-template matTabContent>
          <div class="tab-content-wrapper">
            <h3 class="tab-section-title">Manage App Modules</h3>

            <div class="info-box warning-box mat-elevation-z1"> <!-- Using a class for styling this info box -->
              <mat-icon class="info-icon">warning_amber</mat-icon>
              <div>
                <span>Enable or disable modules and set their access level (Free or Premium).</span>
                <p class="mt-1"><strong>Note:</strong> Some modules may depend on others. You cannot disable a module if other enabled modules depend on it.</p>
              </div>
            </div>

            <div class="table-container mat-elevation-z2">
              <table mat-table [dataSource]="appModulesDataSource" class="admin-mat-table">
                <!-- Module Column -->
                <ng-container matColumnDef="display_name">
                  <th mat-header-cell *matHeaderCellDef> Module </th>
                  <td mat-cell *matCellDef="let module"> {{ module.display_name }} </td>
                </ng-container>

                <!-- Description Column -->
                <ng-container matColumnDef="description">
                  <th mat-header-cell *matHeaderCellDef> Description </th>
                  <td mat-cell *matCellDef="let module"> {{ module.description || 'No description' }} </td>
                </ng-container>

                <!-- Status Column -->
                <ng-container matColumnDef="is_enabled">
                  <th mat-header-cell *matHeaderCellDef> Status </th>
                  <td mat-cell *matCellDef="let module">
                    <span class="status-badge" [class.status-enabled]="module.is_enabled" [class.status-disabled]="!module.is_enabled">
                      {{ module.is_enabled ? 'Enabled' : 'Disabled' }}
                    </span>
                  </td>
                </ng-container>

                <!-- Access Level Column -->
                <ng-container matColumnDef="access_level">
                  <th mat-header-cell *matHeaderCellDef> Access Level </th>
                  <td mat-cell *matCellDef="let module">
                     <span class="status-badge" [class.status-basic]="module.access_level === 'basic'" [class.status-premium]="module.access_level === 'premium'">
                      {{ module.access_level | titlecase }}
                    </span>
                  </td>
                </ng-container>

                <!-- Actions Column -->
                <ng-container matColumnDef="actions">
                  <th mat-header-cell *matHeaderCellDef class="actions-column-header"> Actions </th>
                  <td mat-cell *matCellDef="let module" class="actions-cell">
                    <mat-slide-toggle
                      [checked]="module.is_enabled"
                      (change)="toggleModuleEnabled(module)"
                      [matTooltip]="module.is_enabled ? 'Disable Module' : 'Enable Module'"
                      color="primary"
                      class="mr-2 action-toggle">
                    </mat-slide-toggle>
                    <button mat-stroked-button 
                            (click)="toggleModuleAccessLevel(module)" 
                            [matTooltip]="'Set to ' + (module.access_level === 'basic' ? 'Premium' : 'Basic')"
                            class="action-button">
                      <mat-icon>{{ module.access_level === 'basic' ? 'workspace_premium' : 'price_check' }}</mat-icon>
                      Set to {{ module.access_level === 'basic' ? 'Premium' : 'Basic' }}
                    </button>
                  </td>
                </ng-container>

                <tr mat-header-row *matHeaderRowDef="displayedColumnsModules; sticky: true"></tr>
                <tr mat-row *matRowDef="let row; columns: displayedColumnsModules;"></tr>

                <tr class="mat-row" *matNoDataRow>
                  <td class="mat-cell" [attr.colspan]="displayedColumnsModules.length">
                    No app modules found.
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </ng-template>
      </mat-tab>

      <mat-tab label="Statistics">
        <ng-template matTabContent>
          <div class="tab-content-wrapper stats-tab-wrapper">
            <h3 class="tab-section-title">Application Statistics</h3>

            <div class="stats-grid">
              <mat-card class="stat-card">
                <mat-card-header>
                  <mat-icon mat-card-avatar>people</mat-icon>
                  <mat-card-title>Total Registered Users</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <p class="stat-value">{{ getStatValue('total_users') }}</p>
                </mat-card-content>
              </mat-card>

              <mat-card class="stat-card">
                <mat-card-header>
                  <mat-icon mat-card-avatar>bar_chart</mat-icon>
                  <mat-card-title>Total Entries Added</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <p class="stat-value">{{ getStatValue('total_entries') }}</p>
                </mat-card-content>
              </mat-card>
              
              <mat-card class="stat-card full-width-stat-card"> <!-- Spans full width if only one left or for emphasis -->
                <mat-card-header>
                  <mat-icon mat-card-avatar>calendar_today</mat-icon> <!-- Changed icon for variety -->
                  <mat-card-title>Monthly Active Users</mat-card-title>
                </mat-card-header>
                <mat-card-content>
                  <p class="stat-value">{{ getStatValue('monthly_active_users') }}</p>
                </mat-card-content>
              </mat-card>
            </div>

          </div>
        </ng-template>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
