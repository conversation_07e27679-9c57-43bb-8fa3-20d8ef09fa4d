<div class="signup-page-container">
  <mat-card class="signup-card">
    <mat-card-header>
      <mat-card-title class="signup-title">Sign Up</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <form [formGroup]="signupForm" (ngSubmit)="onSubmit()">
        <mat-form-field appearance="outline" class="full-width-field">
          <mat-label>Email</mat-label>
          <input matInput formControlName="email" type="email" placeholder="<EMAIL>" required>
          <mat-error *ngIf="email?.invalid && (email?.dirty || email?.touched) && email?.errors?.['required']">
            Email is required.
          </mat-error>
          <mat-error *ngIf="email?.invalid && (email?.dirty || email?.touched) && email?.errors?.['email']">
            Please enter a valid email.
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline" class="full-width-field">
          <mat-label>Password</mat-label>
          <input matInput formControlName="password" type="password" placeholder="Password (min 6 characters)" required>
          <mat-error *ngIf="password?.invalid && (password?.dirty || password?.touched) && password?.errors?.['required']">
            Password is required.
          </mat-error>
          <mat-error *ngIf="password?.invalid && (password?.dirty || password?.touched) && password?.errors?.['minlength']">
            Password must be at least 6 characters.
          </mat-error>
        </mat-form-field>

        <div *ngIf="errorMessage" class="server-message error-message">
          <mat-icon>error_outline</mat-icon>
          <span>{{ errorMessage }}</span>
        </div>
        <div *ngIf="successMessage" class="server-message success-message">
          <mat-icon>check_circle_outline</mat-icon>
          <span>{{ successMessage }}</span>
        </div>

        <button mat-raised-button color="primary" type="submit" [disabled]="signupForm.invalid || isLoading" class="full-width-button signup-submit-button">
          <mat-progress-spinner *ngIf="isLoading" mode="indeterminate" diameter="20" class="button-spinner"></mat-progress-spinner>
          {{ isLoading ? 'Signing up...' : 'Sign Up' }}
        </button>
      </form>
    </mat-card-content>
    <mat-card-actions class="signup-card-actions">
      <p>
        Already have an account? <a routerLink="/auth/login" class="nav-link">Login</a>
      </p>
    </mat-card-actions>
  </mat-card>
</div>