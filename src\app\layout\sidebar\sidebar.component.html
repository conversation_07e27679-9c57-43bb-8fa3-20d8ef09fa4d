<div class="sidebar-content-wrapper"> <!-- Added a wrapper for internal structure -->
  <div class="logo">
    <h2>ETB</h2>
  </div>

  <mat-nav-list class="nav-menu">
    <a mat-list-item *ngFor="let item of navItems"
       [routerLink]="item.path"
       routerLinkActive="active-link"
       [routerLinkActiveOptions]="{exact: item.path === '/dashboard'}">
      <mat-icon matListItemIcon>{{ item.icon }}</mat-icon>
      <span matListItemTitle>{{ item.label }}</span>
    </a>
  </mat-nav-list>

  <div class="sidebar-footer">
    <p>Electronic Time Book</p>
  </div>
</div>
