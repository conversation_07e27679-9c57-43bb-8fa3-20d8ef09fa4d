// src/app/auth/signup/signup.component.scss

.signup-page-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 64px); // Assuming 64px is toolbar height, adjust if needed
  padding: var(--spacing-md);
  background-color: var(--surface-color); // Page background
  box-sizing: border-box;
}

.signup-card {
  width: 100%;
  max-width: 400px; // Max width for the signup form card
  padding: var(--spacing-md) 0; 

  .mat-mdc-card-header {
    padding: 0 var(--spacing-lg) var(--spacing-md) var(--spacing-lg); 
  }
  .mat-mdc-card-content {
    padding: 0 var(--spacing-lg) var(--spacing-lg) var(--spacing-lg); 
  }
   .mat-mdc-card-actions {
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: center;
  }
}

.signup-title {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary-color);
  text-align: center;
  width: 100%; 
}

.full-width-field {
  width: 100%;
  margin-bottom: var(--spacing-sm); 
}

.server-message {
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;

  mat-icon {
    margin-right: var(--spacing-xs);
    font-size: var(--font-size-lg); 
  }

  &.error-message {
    background-color: rgba(var(--error-color-rgb), 0.1);
    color: var(--error-color);
    border-left: 3px solid var(--error-color);
    mat-icon { color: var(--error-color); }
  }

  &.success-message {
    background-color: rgba(var(--success-color-rgb), 0.1);
    color: var(--success-color);
    border-left: 3px solid var(--success-color);
    mat-icon { color: var(--success-color); }
  }
}

.full-width-button {
  width: 100%;
  padding: var(--spacing-sm) 0; 
  font-size: var(--font-size-md); 
}

.signup-submit-button {
  margin-top: var(--spacing-sm); 
}

.button-spinner {
  margin-right: var(--spacing-sm);
}

.signup-card-actions p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary-color);
}

.nav-link {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  &:hover {
    text-decoration: underline;
    color: var(--accent-color);
  }
}

// Responsive adjustments
@media (max-width: 600px) {
  .signup-card {
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    padding: 0; 
  }
  .signup-page-container {
     min-height: calc(100vh - 56px); // Assuming 56px is mobile toolbar height
  }
}
