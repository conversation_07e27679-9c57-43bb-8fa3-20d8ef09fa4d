// src/app/features/dashboard/dashboard.component.ts
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, NavigationEnd, RouterModule } from '@angular/router';
import { SupabaseService, Profile } from '../../core/services/supabase.service';
import { User } from '@supabase/supabase-js';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { PayPeriodStatsComponent } from './pay-period-stats/pay-period-stats.component';
import { OvertimeSummaryComponent } from './overtime-summary/overtime-summary.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialog } from '@angular/material/dialog';
import { View998FormatDialogComponent } from './pay-period-stats/view-998-format-dialog.component';
import { OvertimeCalculatorDialogComponent } from './overtime-calculator/overtime-calculator-dialog.component';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    PayPeriodStatsComponent,
    OvertimeSummaryComponent,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnDestroy {
  isLoading = false;
  errorMessage: string | null = null;
  profile: Profile | null = null;
  private userSubscription: Subscription | null = null;
  private routerSubscription: Subscription | null = null;
  private currentUserObject: User | null = null;

  constructor(
    private supabaseService: SupabaseService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private dialog: MatDialog
    ) {}

  ngOnInit(): void {
    // Subscribe to user changes
    this.userSubscription = this.supabaseService.currentUser$
      .pipe(
         // Filter required to ensure user is User object, not null/false
         filter((user): user is User => user !== null && typeof user !== 'boolean')
      )
      .subscribe({
        next: (user: User) => {
          // Store user and load profile
          this.currentUserObject = user;
          this.loadProfile();
        },
        error: (error) => {
          this.isLoading = false;
          this.errorMessage = "Failed to get user session.";
          this.cdr.markForCheck();
        }
      });

    // Subscribe to router navigation events to refresh data when returning to dashboard
    this.routerSubscription = this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd)
      )
      .subscribe((event: NavigationEnd) => {
        // Check if we're navigating to the dashboard route
        if (event.urlAfterRedirects === '/dashboard') {
          // Only reload if we already have a user (prevents double loading on initial navigation)
          if (this.currentUserObject) {
            this.loadProfile();
          }
        }
      });
  }

  ngOnDestroy(): void {
    this.userSubscription?.unsubscribe();
    this.routerSubscription?.unsubscribe();
  }

  /**
   * Opens the 998 Format dialog
   */
  openView998FormatDialog(): void {
    this.dialog.open(View998FormatDialogComponent, {
      width: '600px',
      panelClass: 'custom-dialog-container'
    });
  }

  /**
   * Opens the Overtime Calculator dialog
   */
  openOvertimeCalculator(): void {
    // We don't need to pass data here as the calculator will fetch the current OT hours directly
    this.dialog.open(OvertimeCalculatorDialogComponent, {
      width: '600px',
      panelClass: 'custom-dialog-container'
    });
  }

  // Load user profile
  async loadProfile(): Promise<void> {
    if (!this.currentUserObject) {
      this.profile = null;
      this.isLoading = false;
      this.cdr.markForCheck();
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;
    this.cdr.markForCheck();

    try {
      // Fetch user profile
      this.profile = await this.supabaseService.getProfile(this.currentUserObject.id);
    } catch (error: any) {
      this.errorMessage = error?.message || "Failed to load profile data.";
      this.profile = null;
    } finally {
      this.isLoading = false;
      this.cdr.markForCheck();
    }
  }
} // End of class