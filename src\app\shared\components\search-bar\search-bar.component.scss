// Styles for the search-bar component.
// Note: The sticky positioning is handled by .app-search-bar-sticky in app.component.scss

.search-bar-container {
  // This class is on the root element of search-bar.component.html.
  // Its background, padding, and shadow are applied by .app-search-bar-sticky in app.component.scss
  // when used in the main app layout.
  // If this component were to be used elsewhere with a different wrapper, these styles might be needed here.
  // For now, we assume it's primarily styled by the app.component.scss context.
  width: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.search-bar {
  max-width: 1200px; // Limit width for very wide screens
  margin: 0 auto;    // Center it
  width: 100%;
  display: flex;
  align-items: center;

  // Targeting the mat-form-field within this specific component.
  // We aim to make it more compact for a search bar context.
  mat-form-field {
    width: 100%;
    // Try to avoid ::ng-deep if possible by targeting available CSS custom properties
    // or by more specific selectors if Angular Material structure is stable.

    // To remove the underline/subscript space:
    // Option 1: If you never have hints/errors and want to remove the space:
    // This is the most direct way if no hints/errors are ever shown for this input.
    // Using ::ng-deep because .mat-mdc-form-field-subscript-wrapper is deep.
    ::ng-deep .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    // To adjust padding for a more compact feel:
    // The default padding for outline appearance can be a bit large for a toolbar/search bar.
    // These are internal Material classes, so ::ng-deep is often needed.
    // Be cautious as these class names can change between Angular Material versions.
    ::ng-deep .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
      padding-top: var(--spacing-sm);    // Use 8px from variables
      padding-bottom: var(--spacing-sm); // Use 8px from variables
    }
    
    // Ensure the container itself doesn't add extra height if subscript is hidden
     ::ng-deep .mat-mdc-form-field-flex {
       padding-bottom: 0; // Remove any bottom padding that might remain after subscript is hidden
     }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .search-bar {
    mat-form-field {
      ::ng-deep .mat-mdc-text-field-wrapper.mdc-text-field--outlined .mat-mdc-form-field-infix {
        padding-top: var(--spacing-xs);    // Use 4px for a more compact mobile view
        padding-bottom: var(--spacing-xs); // Use 4px for a more compact mobile view
      }
    }
  }
}