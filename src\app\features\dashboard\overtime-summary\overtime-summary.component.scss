// src/app/features/dashboard/overtime-summary/overtime-summary.component.scss

.overtime-summary-card {
  // Uses default mat-card styling, add specific overrides if needed
  // e.g., margin-bottom: var(--spacing-lg); (This is handled by app-overtime-summary in dashboard.component.scss)
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.summary-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold); // Semibold is often 600, close to medium or bold
  color: var(--text-primary-color);
  display: flex;
  align-items: center;
}

.summary-title-tooltip {
  font-size: var(--font-size-lg); // Slightly smaller than title
  color: var(--text-secondary-color);
  margin-left: var(--spacing-xs);
  cursor: help;
  &:hover {
    color: var(--text-primary-color);
  }
}

.summary-nav {
  display: flex;
  align-items: center;
}

.summary-nav-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary-color);
  margin: 0 var(--spacing-xs);
}

.no-data-message {
  font-size: var(--font-size-md);
  color: var(--text-secondary-color);
  margin-top: var(--spacing-sm);
  text-align: center;
  padding: var(--spacing-md) 0;
}

.period-indicator {
  text-align: center;
  margin-bottom: var(--spacing-md);
}

.period-indicator-text {
  background-color: var(--surface-color);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color); // Or --text-primary-color if preferred
}

.summary-loader {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  gap: var(--spacing-sm);
  color: var(--text-secondary-color);
  font-size: var(--font-size-md);
}

.summary-alert {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-md);
  border-left-width: 4px;
  border-left-style: solid;

  mat-icon {
    margin-right: var(--spacing-sm);
  }
  span {
    font-size: var(--font-size-md);
  }

  &.error-alert {
    background-color: rgba(var(--error-color-rgb), 0.1);
    border-left-color: var(--error-color);
    color: var(--error-color);
    mat-icon {
      color: var(--error-color);
    }
  }
}

.stats-container {
  // No specific styles needed here if stats-grid handles layout
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); // Responsive grid
  gap: var(--spacing-md);
}

.stat-item {
  background-color: var(--surface-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-dp0); // Subtle shadow, or dp1 for more emphasis
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-dp2);
  }
}

.stat-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary-color);
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
}

.stat-tooltip {
  font-size: var(--font-size-md); // Slightly larger than label text for tap target
  color: var(--text-secondary-color);
  margin-left: var(--spacing-xxs);
  cursor: help;
  &:hover {
    color: var(--text-primary-color);
  }
}

.stat-value {
  font-size: var(--font-size-h4); // Larger font for the value
  font-weight: var(--font-weight-bold);
  color: var(--text-primary-color);
}

// Responsive adjustments
@media (max-width: 768px) {
  .summary-header {
    flex-direction: column;
    align-items: flex-start;
  }
  .summary-nav {
    margin-top: var(--spacing-sm);
  }
  .stats-grid {
    grid-template-columns: 1fr; // Stack stats on smaller screens
  }
}
