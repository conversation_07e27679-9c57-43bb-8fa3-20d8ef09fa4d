// src/app/core/services/date-utils.service.ts
import { Injectable } from '@angular/core';

/**
 * Service for handling date operations consistently across the application
 * with special handling for California time zone and daylight saving time.
 */
@Injectable({
  providedIn: 'root'
})
export class DateUtilsService {
  // California time zone
  private readonly CALIFORNIA_TIMEZONE = 'America/Los_Angeles';

  constructor() { }

  /**
   * Formats a date string (YYYY-MM-DD) to a display format
   * @param dateString Date string in YYYY-MM-DD format
   * @param format Format options for display
   * @returns Formatted date string
   */
  formatDate(dateString: string, format: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  }): string {
    if (!dateString) return '';
    
    // Parse the date string manually to avoid time zone issues
    const [year, month, day] = dateString.split('-').map(part => parseInt(part, 10));
    
    // Create a date object with the parsed values (month is 0-indexed in JS Date)
    const date = new Date(year, month - 1, day);
    
    // Format using California time zone
    return date.toLocaleDateString('en-US', {
      ...format,
      timeZone: this.CALIFORNIA_TIMEZONE
    });
  }

  /**
   * Converts a JavaScript Date object to a YYYY-MM-DD string
   * preserving the date as entered without time zone adjustments
   * @param date JavaScript Date object
   * @returns Date string in YYYY-MM-DD format
   */
  toDateString(date: Date): string {
    if (!date) return '';
    
    // Format as YYYY-MM-DD using the date's local components
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  }

  /**
   * Creates a Date object from a form input date string (YYYY-MM-DD)
   * ensuring the date is interpreted correctly regardless of the user's time zone
   * @param dateString Date string in YYYY-MM-DD format from a form input
   * @returns JavaScript Date object
   */
  fromFormDate(dateString: string): Date {
    if (!dateString) return new Date();
    
    // Parse the date string
    const [year, month, day] = dateString.split('-').map(part => parseInt(part, 10));
    
    // Create a date object with the parsed values (month is 0-indexed in JS Date)
    // This preserves the exact date as entered in the form
    return new Date(year, month - 1, day);
  }

  /**
   * Gets the current date in California time zone
   * @returns Current date in California time zone
   */
  getCurrentCaliforniaDate(): Date {
    // Get current date in California time zone
    const options: Intl.DateTimeFormatOptions = { 
      timeZone: this.CALIFORNIA_TIMEZONE,
      year: 'numeric',
      month: 'numeric',
      day: 'numeric'
    };
    
    const formatter = new Intl.DateTimeFormat('en-US', options);
    const parts = formatter.formatToParts(new Date());
    
    // Extract date components
    const year = parseInt(parts.find(part => part.type === 'year')?.value || '0', 10);
    const month = parseInt(parts.find(part => part.type === 'month')?.value || '0', 10) - 1;
    const day = parseInt(parts.find(part => part.type === 'day')?.value || '0', 10);
    
    return new Date(year, month, day);
  }

  /**
   * Checks if a date is within daylight saving time in California
   * @param date Date to check
   * @returns True if the date is within DST
   */
  isInDaylightSavingTime(date: Date): boolean {
    // Get the date in January (standard time) and July (daylight time)
    const jan = new Date(date.getFullYear(), 0, 1);
    const jul = new Date(date.getFullYear(), 6, 1);
    
    // Get the timezone offset for both dates
    const janOffset = jan.getTimezoneOffset();
    const julOffset = jul.getTimezoneOffset();
    
    // If they're different, DST is observed
    if (janOffset !== julOffset) {
      // Get the timezone offset for the provided date
      const dateOffset = date.getTimezoneOffset();
      
      // If the offset is the same as July's, we're in DST
      return dateOffset === julOffset;
    }
    
    // DST is not observed
    return false;
  }
}
