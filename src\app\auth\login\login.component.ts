// src/app/auth/login/login.component.ts
import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink, ActivatedRoute } from '@angular/router';
import { SupabaseService } from '../../core/services/supabase.service';
import { CommonModule } from '@angular/common';

// Angular Material Modules
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner'; // For loading spinner in button
import { MatIconModule } from '@angular/material/icon'; // For icons in messages

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterLink,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatIconModule
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  isLoading = false;
  errorMessage: string | null = null;
  showResendButton = false;
  resendEmail: string = '';
  successMessage: string | null = null;

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    // Initialize the form group
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
    });
  }

  ngOnInit(): void {
    // Check if we need to show the resend verification option
    this.route.queryParams.subscribe(params => {
      if (params['resendVerification'] === 'true') {
        this.showResendButton = true;
        this.errorMessage = 'Please enter your email address to receive a new verification link.';
      }
    });
  }

  // Method to handle form submission
  async onSubmit(): Promise<void> {
    // console.log('Login onSubmit triggered.'); // <-- REMOVED
    if (this.loginForm.invalid) {
      this.errorMessage = 'Please enter a valid email and password.';
      // console.log('Login form invalid.'); // <-- REMOVED
      return;
    }
    // console.log('Login form valid. Setting loading state.'); // <-- REMOVED

    this.isLoading = true;
    this.errorMessage = null;
    const email = this.loginForm.value.email;
    const password = this.loginForm.value.password;

    try {
      // Attempt to sign in using the Supabase service
      const { error } = await this.supabaseService.auth.signInWithPassword({
        email: email,
        password: password,
      });

      if (error) {
        console.log('Login error:', error.message);

        // Handle specific error cases
        if (error.message.includes('Email not confirmed') || error.message.includes('not confirmed')) {
          // Special handling for unverified email
          this.errorMessage = 'Your email has not been verified. Please check your inbox for a verification link or request a new one.';
          // Add a button to resend verification email
          this.showResendButton = true;
          this.resendEmail = email;
        } else {
          throw error; // Let the catch block handle other Supabase errors
        }
      } else {
        // Login successful - SupabaseService listener will update state
        // Navigate to dashboard
        this.router.navigate(['/dashboard']);
      }
    } catch (error: any) {
      // Log the error caught in the catch block
      console.error('Login catch block error:', error);
      // Display specific Supabase error or a generic message
      this.errorMessage = error.message || 'An unexpected error occurred during login.';
    } finally {
      this.isLoading = false; // Reset loading state
    }
  }

  // Getters for easy access to form controls in the template
  get email() { return this.loginForm.get('email'); }
  get password() { return this.loginForm.get('password'); }

  // Method to resend verification email
  async resendVerificationEmail(): Promise<void> {
    if (!this.resendEmail) {
      this.errorMessage = 'Email address is required to resend verification.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;

    try {
      // Get the current URL's origin (e.g., http://localhost:4200)
      const redirectUrl = window.location.origin + '/auth/verification-success';

      // Call Supabase to resend the verification email
      const { error } = await this.supabaseService.auth.resend({
        type: 'signup',
        email: this.resendEmail,
        options: {
          emailRedirectTo: redirectUrl
        }
      });

      if (error) {
        throw error;
      }

      // Update UI to show success
      this.showResendButton = false;
      this.errorMessage = null;
      this.successMessage = 'Verification email has been resent. Please check your inbox and spam folder.';
    } catch (error: any) {
      console.error('Error resending verification email:', error);
      this.errorMessage = error.message || 'Failed to resend verification email. Please try again.';
    } finally {
      this.isLoading = false;
    }
  }

  // Success message property is now declared at the top of the class
}