// src/app/features/calendar/add-entry-dialog/add-entry-dialog.component.ts
import { Component, OnInit, Inject, ElementRef, ViewChild, AfterViewInit, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators, AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatTimepickerModule } from '@angular/material/timepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { SupabaseService, TimeEntry } from '../../../core/services/supabase.service';
import { DateUtilsService } from '../../../core/services/date-utils.service';
import { User } from '@supabase/supabase-js';

export interface AddEntryDialogData {
  dateStr?: string; // Date string in YYYY-MM-DD format
  date?: Date;      // For backward compatibility
  startTime?: string;
  endTime?: string;
  currentUser: User;
}

@Component({
  selector: 'app-add-entry-dialog',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatTimepickerModule,
    MatIconModule,
    MatRadioModule,
    MatCheckboxModule
  ],
  templateUrl: './add-entry-dialog.component.html',
  styleUrls: ['./add-entry-dialog.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AddEntryDialogComponent implements OnInit, AfterViewInit {
  entryForm: FormGroup;
  isLoading = false;
  errorMessage: string | null = null;
  successMessage: string | null = null;

  @ViewChild('cancelButton') cancelButton!: ElementRef;

  // Define standard entry types for the dropdown
  entryTypes = [
    { value: 'REGULAR_WORK', label: 'Regular Work' },
    { value: 'OT_VOLUNTARY', label: 'Overtime (Voluntary)' },
    { value: 'OT_MANDATED', label: 'Overtime (Mandated)' },
    { value: 'VACATION', label: 'Vacation Leave' },
    { value: 'SICK', label: 'Sick Leave' },
    { value: 'ANNUAL_LEAVE', label: 'Annual Leave' },
  ];

  // Define standard watches with 24-hour format times
  watches = [
    { name: '1st Watch (2200 - 0600)', startHour: 22, startMinute: 0, endHour: 6, endMinute: 0, crossesMidnight: true },
    { name: '2nd Watch (0600 - 1400)', startHour: 6, startMinute: 0, endHour: 14, endMinute: 0, crossesMidnight: false },
    { name: '3rd Watch (1400 - 2200)', startHour: 14, startMinute: 0, endHour: 22, endMinute: 0, crossesMidnight: false },
  ];

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    private dateUtils: DateUtilsService,
    public dialogRef: MatDialogRef<AddEntryDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddEntryDialogData
  ) {
    // Get the date to use in the form
    let selectedDate: Date;

    if (data.date) {
      // If a Date object is provided, use it directly
      selectedDate = new Date(data.date);
    } else if (data.dateStr) {
      // If a date string is provided, use DateUtilsService to parse it
      selectedDate = this.dateUtils.fromFormDate(data.dateStr);
    } else {
      // Default to today if neither is provided
      selectedDate = this.dateUtils.getCurrentCaliforniaDate();
    }

    // Log the selected date for debugging
    console.log('Selected date for entry form:', data.dateStr || data.date, 'parsed date:', selectedDate);

    // Create the form with the selected date
    this.entryForm = this.fb.group({
      entryDate: [selectedDate, Validators.required], // Use the Date object directly for the datepicker
      entryType: [this.entryTypes[0].value, Validators.required],
      timeInputMethod: ['watch', Validators.required],
      watchSelection: [this.watches[1], this.timeInputMethodIs('watch')], // Default to 2nd watch (0600-1400)
      startTime: ['', this.timeInputMethodIs('custom')],
      endTime: ['', this.timeInputMethodIs('custom')],
      post_number: [''],
      post_description: [''],
      counts_towards_mandatory: [false],
      notes: ['']
    }, { validators: this.endTimeAfterStartTimeValidator });
  }

  ngOnInit(): void {
    // Configure dialog to close on backdrop click
    this.dialogRef.backdropClick().subscribe(() => {
      // Force immediate close
      setTimeout(() => {
        this.dialogRef.close(false);
      }, 0);
    });

    // If we have time parameters, switch to custom time input
    if (this.data.startTime || this.data.endTime) {
      this.entryForm.patchValue({
        timeInputMethod: 'custom'
      });

      if (this.data.startTime) {
        this.entryForm.patchValue({
          startTime: this.data.startTime
        });
      }

      if (this.data.endTime) {
        this.entryForm.patchValue({
          endTime: this.data.endTime
        });
      }
    }

    // Initialize validators
    this.updateTimeValidators(this.entryForm.get('timeInputMethod')?.value);

    // Set up form value change listeners
    this.entryForm.get('timeInputMethod')?.valueChanges.subscribe(method => {
      this.updateTimeValidators(method);
    });
  }

  // Custom validator factory requires ValidatorFn type
  timeInputMethodIs(method: 'watch' | 'custom'): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const timeInputMethod = control.parent?.get('timeInputMethod')?.value;
      // Check if control is required for the current method AND if it has a value
      if (timeInputMethod === method && !control.value) {
        // Only return error if method matches AND value is missing
        return { required: true };
      }
      return null; // No error if method doesn't match or if value is present
    };
  }

  // Custom validator for the whole form to check end time > start time for custom inputs
  endTimeAfterStartTimeValidator(control: AbstractControl): ValidationErrors | null {
    const method = control.get('timeInputMethod')?.value;
    const startTimeStr = control.get('startTime')?.value;
    const endTimeStr = control.get('endTime')?.value;

    // Only validate if method is custom and both times are present
    if (method !== 'custom' || !startTimeStr || !endTimeStr) {
      return null;
    }

    // Parse time strings (format: HH:MM)
    if (startTimeStr.includes(':') && endTimeStr.includes(':')) {
      const [startHours, startMinutes] = startTimeStr.split(':').map(Number);
      const [endHours, endMinutes] = endTimeStr.split(':').map(Number);

      // Compare hours first, then minutes
      if (endHours < startHours || (endHours === startHours && endMinutes <= startMinutes)) {
        return { endTimeBeforeStart: true };
      }
    }

    return null;
  }

  updateTimeValidators(method: string): void {
    const watchControl = this.entryForm.get('watchSelection');
    const startTimeControl = this.entryForm.get('startTime');
    const endTimeControl = this.entryForm.get('endTime');

    if (method === 'watch') {
      watchControl?.setValidators([Validators.required]);
      startTimeControl?.clearValidators();
      endTimeControl?.clearValidators();
      startTimeControl?.setValue(''); // Clear custom times when switching to watch
      endTimeControl?.setValue('');
    } else { // custom
      watchControl?.clearValidators();
      watchControl?.setValue(null); // Clear watch selection when switching to custom
      startTimeControl?.setValidators([Validators.required]);
      endTimeControl?.setValidators([Validators.required]);
    }
    // Update validity silently to avoid spamming console if errors change immediately
    watchControl?.updateValueAndValidity({ emitEvent: false });
    startTimeControl?.updateValueAndValidity({ emitEvent: false });
    endTimeControl?.updateValueAndValidity({ emitEvent: false });
  }

  async onSubmit(): Promise<void> {
    // Trigger validation display on all fields before final check
    this.entryForm.markAllAsTouched();

    if (this.entryForm.invalid) {
      this.errorMessage = 'Please fill out the required fields correctly.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = null;
    this.successMessage = null;

    const formValue = this.entryForm.value;
    let startDateTimeISO: string;
    let endDateTimeISO: string;

    // Get the selected date from the form (now a Date object)
    const entryDate = formValue.entryDate; // Date object

    try {
      // Combine Date and Time
      if (formValue.timeInputMethod === 'watch') {
        const watch = formValue.watchSelection;

        // Extract date components from the Date object
        const entryYear = entryDate.getFullYear();
        const entryMonth = entryDate.getMonth(); // Already 0-indexed
        const entryDay = entryDate.getDate();

        // Create start date
        let startDate: Date;

        // For 1st watch (2200-0600), the start date should be the previous day
        if (watch.crossesMidnight && watch.startHour === 22) {
          // Create a date for the previous day
          const prevDay = new Date(entryYear, entryMonth, entryDay);
          prevDay.setDate(prevDay.getDate() - 1);

          startDate = new Date(
            prevDay.getFullYear(),
            prevDay.getMonth(),
            prevDay.getDate(),
            watch.startHour,
            watch.startMinute
          );
        } else {
          // Use the selected date
          startDate = new Date(
            entryYear,
            entryMonth,
            entryDay,
            watch.startHour,
            watch.startMinute
          );
        }

        // Create end date
        let endDate: Date;

        if (watch.crossesMidnight) {
          // For 1st watch, end date is the selected date
          endDate = new Date(
            entryYear,
            entryMonth,
            entryDay,
            watch.endHour,
            watch.endMinute
          );
        } else {
          // For other watches, end date is same as start date
          endDate = new Date(
            startDate.getFullYear(),
            startDate.getMonth(),
            startDate.getDate(),
            watch.endHour,
            watch.endMinute
          );
        }

        // Ensure end date is after start date
        if (endDate <= startDate) {
          endDate.setDate(endDate.getDate() + 1);
        }

        // Log the dates for debugging
        console.log('Watch selection - Start date:', startDate, 'End date:', endDate);

        // Convert to ISO strings
        startDateTimeISO = startDate.toISOString();
        endDateTimeISO = endDate.toISOString();

        // Log the ISO strings for debugging
        console.log('Watch selection - Start ISO:', startDateTimeISO, 'End ISO:', endDateTimeISO);

      } else { // Custom Time
        // Extract date components from the Date object
        const entryYear = entryDate.getFullYear();
        const entryMonth = entryDate.getMonth(); // Already 0-indexed
        const entryDay = entryDate.getDate();

        // Parse time from the time picker (format: HH:MM AM/PM or HH:MM in 24h format)
        let startHour = 0;
        let startMinute = 0;
        let endHour = 0;
        let endMinute = 0;

        // Handle time format from ngx-material-timepicker (HH:MM)
        if (formValue.startTime && formValue.startTime.includes(':')) {
          const [hours, minutes] = formValue.startTime.split(':');
          startHour = parseInt(hours, 10);
          startMinute = parseInt(minutes, 10);
        }

        if (formValue.endTime && formValue.endTime.includes(':')) {
          const [hours, minutes] = formValue.endTime.split(':');
          endHour = parseInt(hours, 10);
          endMinute = parseInt(minutes, 10);
        }

        // Create start and end date objects using the selected date and custom times
        const startDateTime = new Date(
          entryYear,
          entryMonth,
          entryDay,
          startHour,
          startMinute
        );

        const endDateTime = new Date(
          entryYear,
          entryMonth,
          entryDay,
          endHour,
          endMinute
        );

        // Handle custom time crossing midnight
        if (endDateTime <= startDateTime) {
          endDateTime.setUTCDate(endDateTime.getUTCDate() + 1);
        }

        // Log the dates for debugging
        console.log('Custom time - Start date:', startDateTime, 'End date:', endDateTime);

        // Convert to ISO strings
        startDateTimeISO = startDateTime.toISOString();
        endDateTimeISO = endDateTime.toISOString();

        // Log the ISO strings for debugging
        console.log('Custom time - Start ISO:', startDateTimeISO, 'End ISO:', endDateTimeISO);
      }

      // Create the time entry
      const newEntry: Omit<TimeEntry, 'id' | 'created_at'> = {
        user_id: this.data.currentUser.id,
        entry_type: formValue.entryType,
        start_time: startDateTimeISO,
        end_time: endDateTimeISO,
        post_number: formValue.post_number || null,
        post_description: formValue.post_description || null,
        notes: formValue.notes || null, // Use null if empty string
        // Only include counts_towards_mandatory if entry type is OT_MANDATED
        counts_towards_mandatory: formValue.entryType === 'OT_MANDATED' ? formValue.counts_towards_mandatory : undefined
      };

      await this.supabaseService.addTimeEntry(newEntry);
      this.successMessage = 'Time entry added successfully!';

      // Close the dialog after a short delay to show the success message
      setTimeout(() => {
        this.dialogRef.close(true);
      }, 1500);

    } catch (error: any) {
      this.errorMessage = error.message || 'Failed to add time entry.';
      this.isLoading = false;
    }
  }

  onCancel(): void {
    // Force immediate close
    setTimeout(() => {
      this.dialogRef.close(false);
    }, 0);
  }

  ngAfterViewInit(): void {
    // Add a direct click handler to the cancel button
    if (this.cancelButton && this.cancelButton.nativeElement) {
      this.cancelButton.nativeElement.addEventListener('click', () => {
        // Force immediate close
        setTimeout(() => {
          this.dialogRef.close(false);
        }, 0);
      });
    }

    // Add a handler for the Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        // Force immediate close
        setTimeout(() => {
          this.dialogRef.close(false);
        }, 0);
      }
    });

    // We're removing the click handler that was causing the dialog to close unexpectedly
    // This was likely causing the issue with the dialog closing when selecting an entry type
  }

  // Helper for template validation access
  get entryDate() { return this.entryForm.get('entryDate'); }
  get entryType() { return this.entryForm.get('entryType'); }
  get timeInputMethod() { return this.entryForm.get('timeInputMethod'); }
  get watchSelection() { return this.entryForm.get('watchSelection'); }
  get startTime() { return this.entryForm.get('startTime'); }
  get endTime() { return this.entryForm.get('endTime'); }


}
