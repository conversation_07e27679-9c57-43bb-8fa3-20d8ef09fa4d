// src/app/features/calendar/event-detail/event-detail.component.ts
import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatTimepickerModule } from '@angular/material/timepicker'; // Added for MatTimepicker
import { SupabaseService, TimeEntry, HolidayEvent } from '../../../core/services/supabase.service';

export interface EventDetailData {
  event: any;
  isHoliday: boolean;
  isReadOnly: boolean;
}

@Component({
  selector: 'app-event-detail',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    MatTimepickerModule // Added
  ],
  templateUrl: './event-detail.component.html',
  styleUrls: ['./event-detail.component.scss']
})
export class EventDetailComponent implements OnInit {
  eventForm: FormGroup;
  isLoading = false;
  errorMessage: string | null = null;
  entryTypes = [
    { value: 'REGULAR_WORK', label: 'Regular Work' },
    { value: 'OT_VOLUNTARY', label: 'Overtime (Voluntary)' },
    { value: 'OT_MANDATED', label: 'Overtime (Mandated)' },
    { value: 'VACATION', label: 'Vacation' },
    { value: 'SICK', label: 'Sick Leave' },
    { value: 'ANNUAL_LEAVE', label: 'Annual Leave' },
    { value: 'PERSONAL_HOLIDAY', label: 'Personal Holiday' }
  ];

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    public dialogRef: MatDialogRef<EventDetailComponent>,
    @Inject(MAT_DIALOG_DATA) public data: EventDetailData
  ) {
    // Create different form structures for holiday vs time entry
    if (data.isHoliday) {
      this.eventForm = this.fb.group({
        id: [null],
        event_name: ['', Validators.required],
        date: [null, Validators.required]
      });
    } else {
      this.eventForm = this.fb.group({
        id: [null],
        entry_type: ['REGULAR_WORK', Validators.required],
        date: [null, Validators.required],
        startTime: ['', Validators.required],
        endTime: ['', Validators.required],
        post_number: [''],
        post_description: [''],
        notes: ['']
      });
    }
  }

  ngOnInit(): void {
    if (this.data.event) {
      if (this.data.isHoliday) {
        // Holiday event
        const holiday = this.data.event.extendedProps.holiday as HolidayEvent;
        this.eventForm.patchValue({
          id: holiday.id,
          event_name: holiday.event_name,
          date: new Date(holiday.event_date)
        });
      } else {
        // Time entry
        const entry = this.data.event.extendedProps.entry as TimeEntry;
        const startDate = new Date(entry.start_time);
        const endDate = new Date(entry.end_time);

        // Format times for the time picker (HH:MM)
        const startHours = startDate.getHours().toString().padStart(2, '0');
        const startMinutes = startDate.getMinutes().toString().padStart(2, '0');
        const endHours = endDate.getHours().toString().padStart(2, '0');
        const endMinutes = endDate.getMinutes().toString().padStart(2, '0');

        this.eventForm.patchValue({
          id: entry.id,
          entry_type: entry.entry_type,
          date: new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate()),
          startTime: `${startHours}:${startMinutes}`,
          endTime: `${endHours}:${endMinutes}`,
          post_number: entry.post_number || '',
          post_description: entry.post_description || '',
          notes: entry.notes || ''
        });
      }
    }

    if (this.data.isReadOnly) {
      this.eventForm.disable();
    }
  }

  async onSubmit(): Promise<void> {
    if (this.eventForm.invalid) return;

    this.isLoading = true;
    this.errorMessage = null;

    try {
      const formValue = this.eventForm.value;

      if (this.data.isHoliday) {
        // Update holiday event
        if (formValue.id) {
          const date = formValue.date as Date;
          const formattedDate = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;

          await this.supabaseService.updateHolidayEvent({
            id: formValue.id,
            event_name: formValue.event_name,
            event_date: formattedDate
          });
        }
      } else {
        // Update time entry
        if (formValue.id) {
          const date = formValue.date as Date;

          // Parse time from HH:MM format
          const [startHours, startMinutes] = formValue.startTime.split(':').map(Number);
          const [endHours, endMinutes] = formValue.endTime.split(':').map(Number);

          const startDate = new Date(date);
          startDate.setHours(startHours, startMinutes, 0, 0);

          const endDate = new Date(date);
          endDate.setHours(endHours, endMinutes, 0, 0);

          // If end time is before start time, assume it's the next day
          if (endDate < startDate) {
            endDate.setDate(endDate.getDate() + 1);
          }

          await this.supabaseService.updateTimeEntry({
            id: formValue.id,
            entry_type: formValue.entry_type,
            start_time: startDate.toISOString(),
            end_time: endDate.toISOString(),
            post_number: formValue.post_number,
            post_description: formValue.post_description,
            notes: formValue.notes
          });
        }
      }

      this.dialogRef.close(true);
    } catch (error: any) {
      console.error('Error updating event:', error);
      this.errorMessage = error.message || 'Failed to update event';
    } finally {
      this.isLoading = false;
    }
  }

  onDelete(): void {
    if (confirm('Are you sure you want to delete this event?')) {
      this.isLoading = true;
      this.errorMessage = null;

      const formValue = this.eventForm.value;

      if (this.data.isHoliday) {
        // Delete holiday event
        if (formValue.id) {
          this.supabaseService.deleteHolidayEvent(formValue.id)
            .then(() => this.dialogRef.close(true))
            .catch(error => {
              console.error('Error deleting holiday event:', error);
              this.errorMessage = error.message || 'Failed to delete event';
            })
            .finally(() => this.isLoading = false);
        }
      } else {
        // Delete time entry
        if (formValue.id) {
          this.supabaseService.deleteTimeEntry(formValue.id)
            .then(() => this.dialogRef.close(true))
            .catch(error => {
              console.error('Error deleting time entry:', error);
              this.errorMessage = error.message || 'Failed to delete event';
            })
            .finally(() => this.isLoading = false);
        }
      }
    }
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }
}
