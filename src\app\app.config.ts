// src/app/app.config.ts
import { ApplicationConfig, importProvidersFrom } from '@angular/core'; // Import importProvidersFrom
import { provideRouter, withHashLocation } from '@angular/router'; // Import withHashLocation
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideHttpClient } from '@angular/common/http';

import { routes } from './app.routes';

// Import FullCalendar modules
import { FullCalendarModule } from '@fullcalendar/angular';

// Import Material modules
import { MatDialogModule } from '@angular/material/dialog';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes, withHashLocation()), // Add withHashLocation() here
    provideAnimations(), // Keep this for animations
    provideHttpClient(),

    // Import FullCalendar and Material modules globally
    importProvidersFrom(
      FullCalendarModule,
      MatDialogModule,
      MatDatepickerModule,
      MatNativeDateModule
    )

    // Other global providers might go here later
  ]
};