.overtime-calculator-container {
  padding: 0;
  max-width: 600px;
}

mat-dialog-content {
  min-height: 300px;
  max-height: 80vh;
  padding: 0 var(--spacing-md); // Use theme variable
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);      // Use theme variable
  margin-bottom: var(--spacing-lg); // Use theme variable

  &:first-of-type {
    margin-top: var(--spacing-md); // Use theme variable
  }
}

.full-width {
  width: 100%;
}

.results-card {
  margin-bottom: var(--spacing-lg); // Use theme variable
  background-color: var(--background-color); // Use theme variable (assuming dialog pane is surface)
}

.results-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm); // Use theme variable
  padding: var(--spacing-sm) 0; // Use theme variable
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-label {
  font-weight: var(--font-weight-medium); // Use theme variable
  color: var(--text-secondary-color);   // Use theme variable
}

.result-value {
  font-weight: var(--font-weight-bold);   // Use theme variable
  color: var(--text-primary-color);     // Use theme variable
}

.total {
  font-size: 1.2em; // Keep relative sizing
}

.total .result-value {
  color: var(--text-primary-color);     // Use theme variable
  font-weight: var(--font-weight-bold);   // Use theme variable
}

.my-3 { // This class seems like a utility, consider replacing its usage if possible
  margin: var(--spacing-sm) 0; // Use theme variable (12px is closer to 8px than 16px)
}

.disclaimer {
  font-size: 0.85em; // Keep relative sizing or map to --font-size-xs if appropriate
  color: var(--text-secondary-color);   // Use theme variable
  background-color: var(--surface-color); // Use theme variable
  padding: var(--spacing-sm);           // Use theme variable
  border-radius: var(--border-radius-sm); // Use theme variable
  margin-top: var(--spacing-md);        // Use theme variable
}

.disclaimer p {
  margin: 0 0 var(--spacing-sm) 0; // Use theme variable
}

.disclaimer p:last-child {
  margin-bottom: 0;
}
